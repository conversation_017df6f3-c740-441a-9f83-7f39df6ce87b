
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"undefined"}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":2,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_autoEmailEnabled":true,"vtp_autoPhoneEnabled":false,"vtp_autoAddressEnabled":false,"vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":10},{"function":"__ccd_ga_first","priority":1,"vtp_instanceDestinationId":"UA-119892649-1","tag_id":13},{"function":"__rep","vtp_containerId":"UA-119892649-1","vtp_remoteConfig":["map"],"tag_id":1},{"function":"__zone","vtp_childContainers":["list",["map","publicId","G-2Y1TJKMJTN"]],"vtp_enableConfiguration":false,"tag_id":3},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"UA-119892649-1","tag_id":12}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",2,3]],[["if",1],["add",0,4,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"EF"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"AA"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CR"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AB"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",47],[52,"h",42],[52,"i",43],[52,"j",44],[52,"k",45],[52,"l",46],[52,"m",56],[52,"n",113],[52,"o",129],[52,"p",142],[52,"q",156],[52,"r",168],[52,"s",174],[52,"t",178],[52,"u",188],[52,"v",199],[52,"w",212],[36,[8,"DX",[15,"r"],"Y",[15,"b"],"AA",[15,"c"],"AB",[15,"d"],"AC",[15,"e"],"AI",[15,"f"],"AL",[15,"h"],"AM",[15,"i"],"AN",[15,"j"],"AO",[15,"k"],"AP",[15,"l"],"AK",[15,"g"],"EK",[15,"u"],"AU",[15,"m"],"EB",[15,"s"],"EF",[15,"t"],"EU",[15,"v"],"CD",[15,"n"],"CR",[15,"o"],"DE",[15,"p"],"FH",[15,"w"],"DP",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"5":true}
,
"__ccd_ga_first":{"2":true,"5":true}
,
"__ccd_ga_last":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__ogt_1p_data_v2":{"2":true,"5":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__c":{}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__e"
,
"__ogt_1p_data_v2"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=da(this),fa=function(a,b){if(b)a:{for(var c=ea,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
fa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ha=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;
if(typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ma;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;ma=pa.a;break a}catch(a){}ma=!1}ja=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=ja,ra=function(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Eq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ta=function(a){return a instanceof Array?a:sa(l(a))},va=function(a){return ua(a,a)},ua=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},wa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};fa("Object.assign",function(a){return a||wa});
var xa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ya=this||self,za=function(a,b){function c(){}c.prototype=b.prototype;a.Eq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Cr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Aa=function(a,b){this.type=a;this.data=b};var Ba=function(){this.map=new Map;this.C=new Set};k=Ba.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.Bl=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Ca=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Ba.prototype.wa=function(){return Ca(this,1)};Ba.prototype.ac=function(){return Ca(this,2)};Ba.prototype.Jb=function(){return Ca(this,3)};var Da=function(){this.map={};this.C={}};k=Da.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.Bl=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ea=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Da.prototype.wa=function(){return Ea(this,1)};Da.prototype.ac=function(){return Ea(this,2)};Da.prototype.Jb=function(){return Ea(this,3)};var Fa=function(){};Fa.prototype.reset=function(){};var Ha=[],Ia={};function Ja(a){return Ha[a]===void 0?!1:Ha[a]};var Ka=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.ub=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ja(16)?new Ba:new Da};k=Ka.prototype;k.add=function(a,b){this.ub||this.values.set(a,b)};k.sh=function(a,b){this.ub||this.values.Bl(a,b)};k.set=function(a,b){this.ub||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.sb=function(){var a=new Ka(this.P,this);this.C&&a.Ob(this.C);a.Xc(this.H);a.Ld(this.N);return a};k.Dd=function(){return this.P};k.Ob=function(a){this.C=a};k.lm=function(){return this.C};k.Xc=function(a){this.H=a};k.kj=function(){return this.H};k.Ua=function(){this.ub=!0};k.Ld=function(a){this.N=a};k.tb=function(){return this.N};var La=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.ub=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.sh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.ub||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.ub||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.sb=function(){var a=new La(this.ba,this);this.H&&a.Ob(this.H);a.Xc(this.N);a.Ld(this.P);return a};k.Dd=function(){return this.ba};k.Ob=function(a){this.H=a};k.lm=function(){return this.H};k.Xc=function(a){this.N=a};k.kj=function(){return this.N};k.Ua=function(){this.ub=!0};k.Ld=function(a){this.P=a};k.tb=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.zm=a;this.dm=c===void 0?!1:c;this.debugInfo=[];this.C=b};ra(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=new Map;function Qa(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ra(a,e.value),c instanceof Aa);e=d.next());return c}function Ra(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f,g=String(d);Ja(18)?(f=Pa.get(g))||(f=a.get(g)):f=a.get(g);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ta(e)))}catch(m){var h=a.lm();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Ta=function(){this.H=new Fa;this.C=Ja(17)?new La(this.H):new Ka(this.H)};k=Ta.prototype;k.Dd=function(){return this.H};k.Ob=function(a){this.C.Ob(a)};k.Xc=function(a){this.C.Xc(a)};k.execute=function(a){return this.Mj([a].concat(ta(xa.apply(1,arguments))))};k.Mj=function(){for(var a,b=l(xa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ra(this.C,c.value);return a};
k.oo=function(a){var b=xa.apply(1,arguments),c=this.C.sb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ra(c,f.value);return d};k.Ua=function(){this.C.Ua()};var Ua=function(){this.Ca=!1;this.aa=new Da};k=Ua.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};k.Ua=function(){this.Ca=!0};k.ub=function(){return this.Ca};function Va(){for(var a=Wa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Za(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Wa,$a;function ab(a){Wa=Wa||Za();$a=$a||Va();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Wa[m],Wa[n],Wa[p],Wa[q])}return b.join("")}
function bb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=$a[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Wa=Wa||Za();$a=$a||Va();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var cb={};function db(a,b){cb[a]=cb[a]||[];cb[a][b]=!0}function eb(){cb.GTAG_EVENT_FEATURE_CHANNEL=fb}function gb(a){var b=cb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return ab(c.join("")).replace(/\.+$/,"")}function hb(){for(var a=[],b=cb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function ib(){}function jb(a){return typeof a==="function"}function lb(a){return typeof a==="string"}function mb(a){return typeof a==="number"&&!isNaN(a)}function nb(a){return Array.isArray(a)?a:[a]}function ob(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function pb(a,b){if(!mb(a)||!mb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function qb(a,b){for(var c=new rb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function sb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function tb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ub(a){return Math.round(Number(a))||0}function vb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function wb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function xb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function yb(){return new Date(Date.now())}function zb(){return yb().getTime()}var rb=function(){this.prefix="gtm.";this.values={}};rb.prototype.set=function(a,b){this.values[this.prefix+a]=b};rb.prototype.get=function(a){return this.values[this.prefix+a]};rb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ab(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Bb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Cb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Db(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Eb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Fb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Gb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Hb=/^\w{1,9}$/;function Ib(a,b){a=a||{};b=b||",";var c=[];sb(a,function(d,e){Hb.test(d)&&e&&c.push(d)});return c.join(b)}function Jb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Kb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Lb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Mb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Nb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ta(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Ob=globalThis.trustedTypes,Pb;function Qb(){var a=null;if(!Ob)return a;try{var b=function(c){return c};a=Ob.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Rb(){Pb===void 0&&(Pb=Qb());return Pb};var Tb=function(a){this.C=a};Tb.prototype.toString=function(){return this.C+""};function Ub(a){var b=a,c=Rb(),d=c?c.createScriptURL(b):b;return new Tb(d)}function Vb(a){if(a instanceof Tb)return a.C;throw Error("");};var Wb=va([""]),Xb=ua(["\x00"],["\\0"]),Yb=ua(["\n"],["\\n"]),Zb=ua(["\x00"],["\\u0000"]);function $b(a){return a.toString().indexOf("`")===-1}$b(function(a){return a(Wb)})||$b(function(a){return a(Xb)})||$b(function(a){return a(Yb)})||$b(function(a){return a(Zb)});var ac=function(a){this.C=a};ac.prototype.toString=function(){return this.C};var bc=function(a){this.Xp=a};function cc(a){return new bc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var dc=[cc("data"),cc("http"),cc("https"),cc("mailto"),cc("ftp"),new bc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ec(a){var b;b=b===void 0?dc:b;if(a instanceof ac)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof bc&&d.Xp(a))return new ac(a)}}var fc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function hc(a){var b;if(a instanceof ac)if(a instanceof ac)b=a.C;else throw Error("");else b=fc.test(a)?a:void 0;return b};function ic(a,b){var c=hc(b);c!==void 0&&(a.action=c)};function jc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var kc=function(a){this.C=a};kc.prototype.toString=function(){return this.C+""};var mc=function(){this.C=lc[0].toLowerCase()};mc.prototype.toString=function(){return this.C};function nc(a,b){var c=[new mc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof mc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var oc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function pc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,qc=window.history,A=document,rc=navigator;function sc(){var a;try{a=rc.serviceWorker}catch(b){return}return a}var tc=A.currentScript,uc=tc&&tc.src;function vc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function wc(a){return(rc.userAgent||"").indexOf(a)!==-1}function xc(){return wc("Firefox")||wc("FxiOS")}function yc(){return(wc("GSA")||wc("GoogleApp"))&&(wc("iPhone")||wc("iPad"))}function zc(){return wc("Edg/")||wc("EdgA/")||wc("EdgiOS/")}
var Ac={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Bc={onload:1,src:1,width:1,height:1,style:1};function Cc(a,b,c){b&&sb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Dc(a,b,c,d,e){var f=A.createElement("script");Cc(f,d,Ac);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Ub(pc(a));f.src=Vb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Ec(){if(uc){var a=uc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Fc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Cc(g,c,Bc);d&&sb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Gc(a,b,c,d){return Hc(a,b,c,d)}function Ic(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Jc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Kc(a){x.setTimeout(a,0)}function Lc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Mc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Nc(a){var b=A.createElement("div"),c=b,d,e=pc("A<div>"+a+"</div>"),f=Rb(),g=f?f.createHTML(e):e;d=new kc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof kc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Oc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Pc(a,b,c){var d;try{d=rc.sendBeacon&&rc.sendBeacon(a)}catch(e){db("TAGGING",15)}d?b==null||b():Hc(a,b,c)}function Qc(a,b){try{return rc.sendBeacon(a,b)}catch(c){db("TAGGING",15)}return!1}var Rc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Tc(a,b,c,d,e){if(Uc()){var f=Object.assign({},Rc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Hh)return e==null||e(),!1;if(b){var h=
Qc(a,b);h?d==null||d():e==null||e();return h}Vc(a,d,e);return!0}function Uc(){return typeof x.fetch==="function"}function Wc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Xc(){var a=x.performance;if(a&&jb(a.now))return a.now()}
function Yc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Zc(){return x.performance||void 0}function $c(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Hc=function(a,b,c,d){var e=new Image(1,1);Cc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Vc=Pc;function ad(a,b){return this.evaluate(a)&&this.evaluate(b)}function bd(a,b){return this.evaluate(a)===this.evaluate(b)}function cd(a,b){return this.evaluate(a)||this.evaluate(b)}function dd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function ed(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function fd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ua&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var gd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,hd=function(a){if(a==null)return String(a);var b=gd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},id=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},jd=function(a){if(!a||hd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!id(a,"constructor")&&!id(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
id(a,b)},kd=function(a,b){var c=b||(hd(a)=="array"?[]:{}),d;for(d in a)if(id(a,d)){var e=a[d];hd(e)=="array"?(hd(c[d])!="array"&&(c[d]=[]),c[d]=kd(e,c[d])):jd(e)?(jd(c[d])||(c[d]={}),c[d]=kd(e,c[d])):c[d]=e}return c};function ld(a){if(a==void 0||Array.isArray(a)||jd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function md(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var nd=function(a){a=a===void 0?[]:a;this.aa=new Da;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(md(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=nd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof nd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!md(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else md(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():md(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Jb=function(){for(var a=this.aa.Jb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){md(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ta(xa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=xa.apply(2,arguments);return b===void 0&&c.length===0?new nd(this.values.splice(a)):new nd(this.values.splice.apply(this.values,[a,b||0].concat(ta(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ta(xa.apply(0,arguments)))};k.has=function(a){return md(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ua=function(){this.Ca=!0;Object.freeze(this.values)};k.ub=function(){return this.Ca};
function od(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var pd=function(a,b){this.functionName=a;this.Ae=b;this.aa=new Da;this.Ca=!1};k=pd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new nd(this.wa())};k.invoke=function(a){return this.Ae.call.apply(this.Ae,[new qd(this,a)].concat(ta(xa.apply(1,arguments))))};k.Mb=function(a){var b=xa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ta(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};
k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};k.Ua=function(){this.Ca=!0};k.ub=function(){return this.Ca};var rd=function(a,b){pd.call(this,a,b)};ra(rd,pd);var sd=function(a,b){pd.call(this,a,b)};ra(sd,pd);var qd=function(a,b){this.Ae=a;this.K=b};
qd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ra(b,a):a};qd.prototype.getName=function(){return this.Ae.getName()};qd.prototype.Dd=function(){return this.K.Dd()};var td=function(){this.map=new Map};td.prototype.set=function(a,b){this.map.set(a,b)};td.prototype.get=function(a){return this.map.get(a)};var ud=function(){this.keys=[];this.values=[]};ud.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};ud.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function vd(){try{return Map?new td:new ud}catch(a){return new ud}};var wd=function(a){if(a instanceof wd)return a;if(ld(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};wd.prototype.getValue=function(){return this.value};wd.prototype.toString=function(){return String(this.value)};var yd=function(a){this.promise=a;this.Ca=!1;this.aa=new Da;this.aa.set("then",xd(this));this.aa.set("catch",xd(this,!0));this.aa.set("finally",xd(this,!1,!0))};k=yd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.ac=function(){return this.aa.ac()};k.Jb=function(){return this.aa.Jb()};
var xd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new rd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof rd||(d=void 0);e instanceof rd||(e=void 0);var f=this.K.sb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new wd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new yd(h)})};yd.prototype.Ua=function(){this.Ca=!0};yd.prototype.ub=function(){return this.Ca};function zd(a,b,c){var d=vd(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof nd){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof yd)return g.promise.then(function(u){return zd(u,b,1)},function(u){return Promise.reject(zd(u,b,1))});if(g instanceof Ua){var q={};d.set(g,q);e(g,q);return q}if(g instanceof rd){var r=function(){for(var u=
xa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Ad(u[w],b,c);var y=new Ka(b?b.Dd():new Fa);b&&y.Ld(b.tb());return f(g.invoke.apply(g,[y].concat(ta(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof wd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Ad(a,b,c){var d=vd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||tb(g)){var m=new nd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(jd(g)){var p=new Ua;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new rd("",function(){for(var u=xa.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=zd(this.evaluate(u[w]),b,c);return f(this.K.kj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new wd(g)};return f(a)};var Bd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof nd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new nd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new nd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new nd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ta(xa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=od(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new nd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=od(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ta(xa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ta(xa.apply(1,arguments)))}};var Cd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Dd=new Aa("break"),Ed=new Aa("continue");function Fd(a,b){return this.evaluate(a)+this.evaluate(b)}function Gd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Hd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof nd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=zd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Cd.hasOwnProperty(e)){var m=2;m=1;var n=zd(f,void 0,m);return Ad(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof nd){if(d.has(e)){var p=d.get(String(e));if(p instanceof rd){var q=od(f);return p.invoke.apply(p,[this.K].concat(ta(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(Bd.supportedMethods.indexOf(e)>=
0){var r=od(f);return Bd[e].call.apply(Bd[e],[d,this.K].concat(ta(r)))}}if(d instanceof rd||d instanceof Ua||d instanceof yd){if(d.has(e)){var t=d.get(e);if(t instanceof rd){var u=od(f);return t.invoke.apply(t,[this.K].concat(ta(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof rd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof wd&&e==="toString")return d.toString();throw Oa(Error("TypeError: Object has no '"+
e+"' property."));}function Id(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Jd(){var a=xa.apply(0,arguments),b=this.K.sb(),c=Qa(b,a);if(c instanceof Aa)return c}function Kd(){return Dd}function Ld(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Aa)return d}}
function Md(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.sh(c,d)}}}function Nd(){return Ed}function Od(a,b){return new Aa(a,this.evaluate(b))}function Pd(a,b){for(var c=xa.apply(2,arguments),d=new nd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ta(c));this.K.add(a,this.evaluate(g))}function Qd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Rd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof wd,f=d instanceof wd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Sd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Td(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Qa(f,d);if(g instanceof Aa){if(g.type==="break")break;if(g.type==="return")return g}}}
function Ud(a,b,c){if(typeof b==="string")return Td(a,function(){return b.length},function(f){return f},c);if(b instanceof Ua||b instanceof yd||b instanceof nd||b instanceof rd){var d=b.wa(),e=d.length;return Td(a,function(){return e},function(f){return d[f]},c)}}function Vd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Ud(function(h){g.set(d,h);return g},e,f)}
function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Ud(function(h){var m=g.sb();m.sh(d,h);return m},e,f)}function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Ud(function(h){var m=g.sb();m.add(d,h);return m},e,f)}function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){g.set(d,h);return g},e,f)}
function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){var m=g.sb();m.sh(d,h);return m},e,f)}function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Zd(function(h){var m=g.sb();m.add(d,h);return m},e,f)}
function Zd(a,b,c){if(typeof b==="string")return Td(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof nd)return Td(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function be(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof nd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.sb();for(e(g,m);Ra(m,b);){var n=Qa(m,h);if(n instanceof Aa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.sb();e(m,p);Ra(p,c);m=p}}
function ce(a,b){var c=xa.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof nd))throw Error("Error: non-List value given for Fn argument names.");return new rd(a,function(){return function(){var f=xa.apply(0,arguments),g=d.sb();g.tb()===void 0&&g.Ld(this.K.tb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new nd(h));var r=Qa(g,c);if(r instanceof Aa)return r.type===
"return"?r.data:r}}())}function de(a){var b=this.evaluate(a),c=this.K;if(ee&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function fe(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ua||d instanceof yd||d instanceof nd||d instanceof rd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:md(e)&&(c=d[e]);else if(d instanceof wd)return;return c}function ge(a,b){return this.evaluate(a)>this.evaluate(b)}function he(a,b){return this.evaluate(a)>=this.evaluate(b)}
function ie(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof wd&&(c=c.getValue());d instanceof wd&&(d=d.getValue());return c===d}function je(a,b){return!ie.call(this,a,b)}function ke(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Qa(this.K,d);if(e instanceof Aa)return e}var ee=!1;
function le(a,b){return this.evaluate(a)<this.evaluate(b)}function me(a,b){return this.evaluate(a)<=this.evaluate(b)}function ne(){for(var a=new nd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function oe(){for(var a=new Ua,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function pe(a,b){return this.evaluate(a)%this.evaluate(b)}
function qe(a,b){return this.evaluate(a)*this.evaluate(b)}function re(a){return-this.evaluate(a)}function se(a){return!this.evaluate(a)}function te(a,b){return!Rd.call(this,a,b)}function ue(){return null}function ve(a,b){return this.evaluate(a)||this.evaluate(b)}function we(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function xe(a){return this.evaluate(a)}function ye(){return xa.apply(0,arguments)}function ze(a){return new Aa("return",this.evaluate(a))}
function Ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof rd||d instanceof nd||d instanceof Ua)&&d.set(String(e),f);return f}function Be(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Aa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Aa&&(g.type==="return"||g.type==="continue")))return g}
function De(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Ee(a){var b=this.evaluate(a);return b instanceof rd?"function":typeof b}function Fe(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ge(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Qa(this.K,e);if(f instanceof Aa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Qa(this.K,e);if(g instanceof Aa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function He(a){return~Number(this.evaluate(a))}function Ie(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Je(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ke(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Le(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Oe(){}
function Pe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Aa)return d}catch(h){if(!(h instanceof Na&&h.dm))throw h;var e=this.K.sb();a!==""&&(h instanceof Na&&(h=h.zm),e.add(a,new wd(h)));var f=this.evaluate(c),g=Qa(e,f);if(g instanceof Aa)return g}}function Qe(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.dm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Aa)return e;if(c)throw c;if(d instanceof Aa)return d};var Se=function(){this.C=new Ta;Re(this)};Se.prototype.execute=function(a){return this.C.Mj(a)};var Re=function(a){var b=function(c,d){var e=new sd(String(c),d);e.Ua();var f=String(c);a.C.C.set(f,e);Pa.set(f,e)};b("map",oe);b("and",ad);b("contains",dd);b("equals",bd);b("or",cd);b("startsWith",ed);b("variable",fd)};Se.prototype.Ob=function(a){this.C.Ob(a)};var Ue=function(){this.H=!1;this.C=new Ta;Te(this);this.H=!0};Ue.prototype.execute=function(a){return Ve(this.C.Mj(a))};var We=function(a,b,c){return Ve(a.C.oo(b,c))};Ue.prototype.Ua=function(){this.C.Ua()};
var Te=function(a){var b=function(c,d){var e=String(c),f=new sd(e,d);f.Ua();a.C.C.set(e,f);Pa.set(e,f)};b(0,Fd);b(1,Gd);b(2,Hd);b(3,Id);b(56,Le);b(57,Ie);b(58,He);b(59,Ne);b(60,Je);b(61,Ke);b(62,Me);b(53,Jd);b(4,Kd);b(5,Ld);b(68,Pe);b(52,Md);b(6,Nd);b(49,Od);b(7,ne);b(8,oe);b(9,Ld);b(50,Pd);b(10,Qd);b(12,Rd);b(13,Sd);b(67,Qe);b(51,ce);b(47,Vd);b(54,Wd);b(55,Xd);b(63,be);b(64,Yd);b(65,$d);b(66,ae);b(15,de);b(16,fe);b(17,fe);b(18,ge);b(19,he);b(20,ie);b(21,je);b(22,ke);b(23,le);b(24,me);b(25,pe);b(26,
qe);b(27,re);b(28,se);b(29,te);b(45,ue);b(30,ve);b(32,we);b(33,we);b(34,xe);b(35,xe);b(46,ye);b(36,ze);b(43,Ae);b(37,Be);b(38,Ce);b(39,De);b(40,Ee);b(44,Oe);b(41,Fe);b(42,Ge)};Ue.prototype.Dd=function(){return this.C.Dd()};Ue.prototype.Ob=function(a){this.C.Ob(a)};Ue.prototype.Xc=function(a){this.C.Xc(a)};
function Ve(a){if(a instanceof Aa||a instanceof rd||a instanceof nd||a instanceof Ua||a instanceof yd||a instanceof wd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Xe=function(a){this.message=a};function Ye(a){a.Jr=!0;return a};var Ze=Ye(function(a){return typeof a==="string"});function $e(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Xe("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function af(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var bf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function cf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+$e(e)+c}a<<=2;d||(a|=32);return c=""+$e(a|b)+c}
function df(a,b){var c;var d=a.Wc,e=a.Uc;d===void 0?c="":(e||(e=0),c=""+cf(1,1)+$e(d<<2|e));var f=a.bm,g=a.Ro,h="4"+c+(f?""+cf(2,1)+$e(f):"")+(g?""+cf(12,1)+$e(g):""),m,n=a.Nj;m=n&&bf.test(n)?""+cf(3,2)+n:"";var p,q=a.Jj;p=q?""+cf(4,1)+$e(q):"";var r;var t=a.ctid;if(t&&b){var u=cf(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+$e(1+y.length)+(a.rm||0)+y}}else r="";var z=a.Dq,B=a.xe,D=a.Ma,F=a.Nr,H=h+m+p+r+(z?""+cf(6,1)+$e(z):"")+(B?""+cf(7,3)+$e(B.length)+
B:"")+(D?""+cf(8,3)+$e(D.length)+D:"")+(F?""+cf(9,3)+$e(F.length)+F:""),J;var S=a.fm;S=S===void 0?{}:S;for(var ca=[],U=l(Object.keys(S)),na=U.next();!na.done;na=U.next()){var T=na.value;ca[Number(T)]=S[T]}if(ca.length){var Z=cf(10,3),X;if(ca.length===0)X=$e(0);else{for(var V=[],ka=0,ia=!1,la=0;la<ca.length;la++){ia=!0;var Sa=la%6;ca[la]&&(ka|=1<<Sa);Sa===5&&(V.push($e(ka)),ka=0,ia=!1)}ia&&V.push($e(ka));X=V.join("")}var Ya=X;J=""+Z+$e(Ya.length)+Ya}else J="";var Ga=a.Am,Xa=a.tq;return H+J+(Ga?""+
cf(11,3)+$e(Ga.length)+Ga:"")+(Xa?""+cf(13,3)+$e(Xa.length)+Xa:"")};var ef=function(){function a(b){return{toString:function(){return b}}}return{Ym:a("consent"),ek:a("convert_case_to"),fk:a("convert_false_to"),gk:a("convert_null_to"),hk:a("convert_true_to"),ik:a("convert_undefined_to"),Pq:a("debug_mode_metadata"),Ra:a("function"),Ei:a("instance_name"),ro:a("live_only"),so:a("malware_disabled"),METADATA:a("metadata"),wo:a("original_activity_id"),kr:a("original_vendor_template_id"),jr:a("once_on_load"),vo:a("once_per_event"),Dl:a("once_per_load"),mr:a("priority_override"),
rr:a("respected_consent_types"),Ml:a("setup_tags"),qh:a("tag_id"),Ul:a("teardown_tags")}}();var Bf;var Cf=[],Df=[],Ef=[],Ff=[],Gf=[],Hf,Jf,Kf;function Lf(a){Kf=Kf||a}
function Mf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Cf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Ff.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Ef.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Nf(p[r])}Df.push(p)}}
function Nf(a){}var Of,Pf=[],Qf=[];function Rf(a,b){var c={};c[ef.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Sf(a,b,c){try{return Jf(Tf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Tf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Uf(a[e],b,c));return d},Uf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Uf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Cf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[ef.Ei]);try{var m=Tf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Vf(m,{event:b,index:f,type:2,
name:h});Of&&(d=Of.Uo(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Uf(a[n],b,c)]=Uf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Uf(a[q],b,c);Kf&&(p=p||Kf.Up(r));d.push(r)}return Kf&&p?Kf.Zo(d):d.join("");case "escape":d=Uf(a[1],b,c);if(Kf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Kf.Vp(a))return Kf.lq(d);d=String(d);for(var t=2;t<a.length;t++)mf[a[t]]&&(d=mf[a[t]](d));return d;
case "tag":var u=a[1];if(!Ff[u])throw Error("Unable to resolve tag reference "+u+".");return{jm:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[ef.Ra]=a[1];var w=Sf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Vf=function(a,b){var c=a[ef.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Hf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Pf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Eb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Cf[q];break;case 1:r=Ff[q];break;default:n="";break a}var t=r&&r[ef.Ei];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Qf.indexOf(c)===-1){Qf.push(c);
var y=zb();u=e(g);var z=zb()-y,B=zb();v=Bf(c,h,b);w=z-(zb()-B)}else if(e&&(u=e(g)),!e||f)v=Bf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),ld(u)?(Array.isArray(u)?Array.isArray(v):jd(u)?jd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Wf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Wf,Error);Wf.prototype.getMessage=function(){return this.message};function Xf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Xf(a[c],b[c])}};function Yf(){return function(a,b){var c;var d=Zf;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Zf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)mb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function $f(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=ag(a),f=0;f<Df.length;f++){var g=Df[f],h=bg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Ff.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function bg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function ag(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Sf(Ef[c],a));return b[c]}};function cg(a,b){b[ef.ek]&&typeof a==="string"&&(a=b[ef.ek]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(ef.gk)&&a===null&&(a=b[ef.gk]);b.hasOwnProperty(ef.ik)&&a===void 0&&(a=b[ef.ik]);b.hasOwnProperty(ef.hk)&&a===!0&&(a=b[ef.hk]);b.hasOwnProperty(ef.fk)&&a===!1&&(a=b[ef.fk]);return a};var dg=function(){this.C={}},fg=function(a,b){var c=eg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ta(xa.apply(0,arguments)))})};function gg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Wf(c,d,g);}}
function hg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ta(xa.apply(1,arguments))));gg(e,b,d,g);gg(f,b,d,g)}}}};var lg=function(){var a=data.permissions||{},b=ig.ctid,c=this;this.H={};this.C=new dg;var d={},e={},f=hg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ta(xa.apply(1,arguments)))):{}});sb(a,function(g,h){function m(p){var q=xa.apply(1,arguments);if(!n[p])throw jg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ta(q)))}var n={};sb(h,function(p,q){var r=kg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Zl&&!e[p]&&(e[p]=r.Zl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw jg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ta(t.slice(1))))}})},mg=function(a){return eg.H[a]||function(){}};
function kg(a,b){var c=Rf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=jg;try{return Vf(c)}catch(d){return{assert:function(e){throw new Wf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Wf(a,{},"Permission "+a+" is unknown.");}}}}function jg(a,b,c){return new Wf(a,b,c)};var ng=!1;var og={};og.Rm=vb('');og.lp=vb('');function tg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var ug=[];function vg(a){switch(a){case 1:return 0;case 38:return 13;case 53:return 1;case 54:return 2;case 52:return 7;case 211:return 18;case 75:return 3;case 103:return 14;case 197:return 15;case 203:return 16;case 114:return 12;case 115:return 4;case 116:return 5;case 209:return 17;case 135:return 9;case 136:return 6}}function wg(a,b){ug[a]=b;var c=vg(a);c!==void 0&&(Ha[c]=b)}function C(a){wg(a,!0)}
C(39);C(34);C(35);C(36);
C(56);C(145);C(153);C(144);C(120);
C(5);C(111);C(139);C(87);
C(92);C(159);C(132);
C(20);C(72);C(113);
C(154);C(116);C(143);
wg(23,!1),C(24);
Ia[1]=tg('1',6E4);Ia[3]=tg('10',1);Ia[2]=tg('',50);C(29);
xg(26,25);
C(37);C(9);
C(91);C(123);
C(158);C(71);C(136);C(127);C(27);C(69);C(135);
C(95);C(38);C(103);C(112);
C(63);
C(152);
C(101);C(122);C(121);
C(108);
C(134);C(115);C(31);
C(22);C(97);C(19);
C(90);
C(59);C(13);
C(175);C(176);
C(183);C(185);C(186);C(187);C(192);
C(199);C(201);


function E(a){return!!ug[a]}function xg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?C(b):C(a)};var zg={},Ag=(zg.uaa=!0,zg.uab=!0,zg.uafvl=!0,zg.uamb=!0,zg.uam=!0,zg.uap=!0,zg.uapv=!0,zg.uaw=!0,zg);
var Ig=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Gg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Hg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Eb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Hg=/^[a-z$_][\w-$]*$/i,Gg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Jg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Kg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Lg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Mg=new rb;function Ng(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Mg.get(e);f||(f=new RegExp(b,d),Mg.set(e,f));return f.test(a)}catch(g){return!1}}function Og(a,b){return String(a).indexOf(String(b))>=0}
function Pg(a,b){return String(a)===String(b)}function Qg(a,b){return Number(a)>=Number(b)}function Rg(a,b){return Number(a)<=Number(b)}function Sg(a,b){return Number(a)>Number(b)}function Tg(a,b){return Number(a)<Number(b)}function Ug(a,b){return Eb(String(a),String(b))};var ah=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,bh={Fn:"function",PixieMap:"Object",List:"Array"};
function ch(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=ah.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof rd?n="Fn":m instanceof nd?n="List":m instanceof Ua?n="PixieMap":m instanceof yd?n="PixiePromise":m instanceof wd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((bh[n]||n)+", which does not match required type ")+
((bh[h]||h)+"."));}}}function G(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof rd?d.push("function"):g instanceof nd?d.push("Array"):g instanceof Ua?d.push("Object"):g instanceof yd?d.push("Promise"):g instanceof wd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function dh(a){return a instanceof Ua}function eh(a){return dh(a)||a===null||fh(a)}
function gh(a){return a instanceof rd}function hh(a){return gh(a)||a===null||fh(a)}function ih(a){return a instanceof nd}function jh(a){return a instanceof wd}function kh(a){return typeof a==="string"}function lh(a){return kh(a)||a===null||fh(a)}function mh(a){return typeof a==="boolean"}function nh(a){return mh(a)||fh(a)}function oh(a){return mh(a)||a===null||fh(a)}function ph(a){return typeof a==="number"}function fh(a){return a===void 0};function qh(a){return""+a}
function rh(a,b){var c=[];return c};function sh(a,b){var c=new rd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Ua();return c}
function th(a,b){var c=new Ua,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];jb(e)?c.set(d,sh(a+"_"+d,e)):jd(e)?c.set(d,th(a+"_"+d,e)):(mb(e)||lb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ua();return c};function uh(a,b){if(!kh(a))throw G(this.getName(),["string"],arguments);if(!lh(b))throw G(this.getName(),["string","undefined"],arguments);var c={},d=new Ua;return d=th("AssertApiSubject",
c)};function vh(a,b){if(!lh(b))throw G(this.getName(),["string","undefined"],arguments);if(a instanceof yd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ua;return d=th("AssertThatSubject",c)};function wh(a){return function(){for(var b=xa.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(zd(b[e],d));return Ad(a.apply(null,c))}}function xh(){for(var a=Math,b=yh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=wh(a[e].bind(a)))}return c};function zh(a){return a!=null&&Eb(a,"__cvt_")};function Ah(a){var b;return b};function Bh(a){var b;return b};function Ch(a){try{return encodeURI(a)}catch(b){}};function Dh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Ih(a){if(!lh(a))throw G(this.getName(),["string|undefined"],arguments);};function Jh(a,b){if(!ph(a)||!ph(b))throw G(this.getName(),["number","number"],arguments);return pb(a,b)};function Kh(){return(new Date).getTime()};function Lh(a){if(a===null)return"null";if(a instanceof nd)return"array";if(a instanceof rd)return"function";if(a instanceof wd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Mh(a){function b(c){return function(d){try{return c(d)}catch(e){(ng||og.Rm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Ad(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(zd(c))}),publicName:"JSON"}};function Nh(a){return ub(zd(a,this.K))};function Oh(a){return Number(zd(a,this.K))};function Ph(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Qh(a,b,c){var d=null,e=!1;return e?d:null};var yh="floor ceil round max min abs pow sqrt".split(" ");function Rh(){var a={};return{xp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Mm:function(b,c){a[b]=c},reset:function(){a={}}}}function Sh(a,b){return function(){return rd.prototype.invoke.apply(a,[b].concat(ta(xa.apply(0,arguments))))}}
function Th(a,b){if(!kh(a))throw G(this.getName(),["string","any"],arguments);}
function Uh(a,b){if(!kh(a)||!dh(b))throw G(this.getName(),["string","PixieMap"],arguments);};var Vh={};var Wh=function(a){var b=new Ua;if(a instanceof nd)for(var c=a.wa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof rd)for(var f=a.wa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Vh.keys=function(a){ch(this.getName(),arguments);if(a instanceof nd||a instanceof rd||typeof a==="string")a=Wh(a);if(a instanceof Ua||a instanceof yd)return new nd(a.wa());return new nd};
Vh.values=function(a){ch(this.getName(),arguments);if(a instanceof nd||a instanceof rd||typeof a==="string")a=Wh(a);if(a instanceof Ua||a instanceof yd)return new nd(a.ac());return new nd};
Vh.entries=function(a){ch(this.getName(),arguments);if(a instanceof nd||a instanceof rd||typeof a==="string")a=Wh(a);if(a instanceof Ua||a instanceof yd)return new nd(a.Jb().map(function(b){return new nd(b)}));return new nd};
Vh.freeze=function(a){(a instanceof Ua||a instanceof yd||a instanceof nd||a instanceof rd)&&a.Ua();return a};Vh.delete=function(a,b){if(a instanceof Ua&&!a.ub())return a.remove(b),!0;return!1};function I(a,b){var c=xa.apply(2,arguments),d=a.K.tb();if(!d)throw Error("Missing program state.");if(d.rq){try{d.am.apply(null,[b].concat(ta(c)))}catch(e){throw db("TAGGING",21),e;}return}d.am.apply(null,[b].concat(ta(c)))};var Xh=function(){this.H={};this.C={};this.N=!0;};Xh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Xh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Xh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:jb(b)?sh(a,b):th(a,b)};function Yh(a,b){var c=void 0;return c};function Zh(){var a={};return a};var K={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ia:"analytics_storage",jc:"region",ja:"consent_updated",vg:"wait_for_update",mn:"app_remove",nn:"app_store_refund",on:"app_store_subscription_cancel",pn:"app_store_subscription_convert",qn:"app_store_subscription_renew",rn:"consent_update",mk:"add_payment_info",nk:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",pk:"view_cart",Zc:"begin_checkout",Rd:"select_item",mc:"view_item_list",Ic:"select_promotion",nc:"view_promotion",
lb:"purchase",Sd:"refund",yb:"view_item",qk:"add_to_wishlist",sn:"exception",tn:"first_open",un:"first_visit",qa:"gtag.config",Db:"gtag.get",vn:"in_app_purchase",bd:"page_view",wn:"screen_view",xn:"session_start",yn:"source_update",zn:"timing_complete",An:"track_social",Td:"user_engagement",Bn:"user_id_update",Me:"gclid_link_decoration_source",Ne:"gclid_storage_source",oc:"gclgb",mb:"gclid",rk:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",za:"ads_data_redaction",Oe:"gad_source",Pe:"gad_source_src",
dd:"gclid_url",sk:"gclsrc",Qe:"gbraid",Xd:"wbraid",Ea:"allow_ad_personalization_signals",Cg:"allow_custom_scripts",Re:"allow_direct_google_requests",Dg:"allow_display_features",Eg:"allow_enhanced_conversions",Pb:"allow_google_signals",nb:"allow_interest_groups",Cn:"app_id",Dn:"app_installer_id",En:"app_name",Gn:"app_version",Qb:"auid",Hn:"auto_detection_enabled",ed:"aw_remarketing",Th:"aw_remarketing_only",Fg:"discount",Gg:"aw_feed_country",Hg:"aw_feed_language",sa:"items",Ig:"aw_merchant_id",tk:"aw_basket_type",
Se:"campaign_content",Te:"campaign_id",Ue:"campaign_medium",Ve:"campaign_name",We:"campaign",Xe:"campaign_source",Ye:"campaign_term",Rb:"client_id",uk:"rnd",Uh:"consent_update_type",In:"content_group",Jn:"content_type",Sb:"conversion_cookie_prefix",Ze:"conversion_id",Oa:"conversion_linker",Vh:"conversion_linker_disabled",fd:"conversion_api",Jg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",zb:"cookie_flags",gd:"cookie_name",Tb:"cookie_path",eb:"cookie_prefix",Jc:"cookie_update",Yd:"country",
Va:"currency",Wh:"customer_buyer_stage",af:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",bf:"custom_map",Zh:"gcldc",hd:"dclid",vk:"debug_mode",oa:"developer_id",Kn:"disable_merchant_reported_purchases",jd:"dc_custom_params",Ln:"dc_natural_search",wk:"dynamic_event_settings",xk:"affiliation",Kg:"checkout_option",ai:"checkout_step",yk:"coupon",cf:"item_list_name",bi:"list_name",Mn:"promotions",df:"shipping",di:"tax",Lg:"engagement_time_msec",Mg:"enhanced_client_id",ei:"enhanced_conversions",
zk:"enhanced_conversions_automatic_settings",Ng:"estimated_delivery_date",fi:"euid_logged_in_state",ef:"event_callback",Nn:"event_category",Ub:"event_developer_id_string",On:"event_label",kd:"event",Og:"event_settings",Pg:"event_timeout",Pn:"description",Qn:"fatal",Rn:"experiments",gi:"firebase_id",Zd:"first_party_collection",Qg:"_x_20",rc:"_x_19",Ak:"fledge_drop_reason",Bk:"fledge",Ck:"flight_error_code",Dk:"flight_error_message",Ek:"fl_activity_category",Fk:"fl_activity_group",hi:"fl_advertiser_id",
Gk:"fl_ar_dedupe",ff:"match_id",Hk:"fl_random_number",Ik:"tran",Jk:"u",Rg:"gac_gclid",ae:"gac_wbraid",Kk:"gac_wbraid_multiple_conversions",Lk:"ga_restrict_domain",ii:"ga_temp_client_id",Sn:"ga_temp_ecid",ld:"gdpr_applies",Mk:"geo_granularity",Kc:"value_callback",sc:"value_key",uc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Nk:"google_tld",hf:"gpp_sid",jf:"gpp_string",Sg:"groups",Ok:"gsa_experiment_id",kf:"gtag_event_feature_usage",Pk:"gtm_up",Lc:"iframe_state",lf:"ignore_referrer",
ji:"internal_traffic_results",Qk:"_is_fpm",Mc:"is_legacy_converted",Nc:"is_legacy_loaded",Tg:"is_passthrough",md:"_lps",Ab:"language",Ug:"legacy_developer_id_string",Pa:"linker",de:"accept_incoming",vc:"decorate_forms",ma:"domains",Oc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Rk:"method",Tn:"name",Sk:"navigation_type",nf:"new_customer",Vg:"non_interaction",Un:"optimize_id",Tk:"page_hostname",pf:"page_path",Wa:"page_referrer",Eb:"page_title",Uk:"passengers",
Vk:"phone_conversion_callback",Vn:"phone_conversion_country_code",Wk:"phone_conversion_css_class",Wn:"phone_conversion_ids",Xk:"phone_conversion_number",Yk:"phone_conversion_options",Xn:"_platinum_request_status",Yn:"_protected_audience_enabled",qf:"quantity",Wg:"redact_device_info",ki:"referral_exclusion_definition",Sq:"_request_start_time",Wb:"restricted_data_processing",Zn:"retoken",ao:"sample_rate",li:"screen_name",Pc:"screen_resolution",Zk:"_script_source",bo:"search_term",qb:"send_page_view",
nd:"send_to",od:"server_container_url",rf:"session_duration",Xg:"session_engaged",mi:"session_engaged_time",wc:"session_id",Yg:"session_number",tf:"_shared_user_id",uf:"delivery_postal_code",Tq:"_tag_firing_delay",Uq:"_tag_firing_time",Vq:"temporary_client_id",ni:"_timezone",oi:"topmost_url",co:"tracking_id",ri:"traffic_type",Xa:"transaction_id",xc:"transport_url",al:"trip_type",rd:"update",Fb:"url_passthrough",bl:"uptgs",vf:"_user_agent_architecture",wf:"_user_agent_bitness",xf:"_user_agent_full_version_list",
yf:"_user_agent_mobile",zf:"_user_agent_model",Af:"_user_agent_platform",Bf:"_user_agent_platform_version",Cf:"_user_agent_wow64",fb:"user_data",si:"user_data_auto_latency",ui:"user_data_auto_meta",wi:"user_data_auto_multi",xi:"user_data_auto_selectors",yi:"user_data_auto_status",yc:"user_data_mode",Zg:"user_data_settings",Qa:"user_id",Xb:"user_properties",fl:"_user_region",Df:"us_privacy_string",Fa:"value",il:"wbraid_multiple_conversions",ud:"_fpm_parameters",Ci:"_host_name",sl:"_in_page_command",
tl:"_ip_override",yl:"_is_passthrough_cid",zc:"non_personalized_ads",Oi:"_sst_parameters",qc:"conversion_label",Aa:"page_location",Vb:"global_developer_id_string",pd:"tc_privacy_string"}};var $h={},ai=($h[K.m.ja]="gcu",$h[K.m.oc]="gclgb",$h[K.m.mb]="gclaw",$h[K.m.rk]="gclid_len",$h[K.m.Ud]="gclgs",$h[K.m.Vd]="gcllp",$h[K.m.Wd]="gclst",$h[K.m.Qb]="auid",$h[K.m.Fg]="dscnt",$h[K.m.Gg]="fcntr",$h[K.m.Hg]="flng",$h[K.m.Ig]="mid",$h[K.m.tk]="bttype",$h[K.m.Rb]="gacid",$h[K.m.qc]="label",$h[K.m.fd]="capi",$h[K.m.Jg]="pscdl",$h[K.m.Va]="currency_code",$h[K.m.Wh]="clobs",$h[K.m.af]="vdltv",$h[K.m.Xh]="clolo",$h[K.m.Yh]="clolb",$h[K.m.vk]="_dbg",$h[K.m.Ng]="oedeld",$h[K.m.Ub]="edid",$h[K.m.Ak]=
"fdr",$h[K.m.Bk]="fledge",$h[K.m.Rg]="gac",$h[K.m.ae]="gacgb",$h[K.m.Kk]="gacmcov",$h[K.m.ld]="gdpr",$h[K.m.Vb]="gdid",$h[K.m.be]="_ng",$h[K.m.hf]="gpp_sid",$h[K.m.jf]="gpp",$h[K.m.Ok]="gsaexp",$h[K.m.kf]="_tu",$h[K.m.Lc]="frm",$h[K.m.Tg]="gtm_up",$h[K.m.md]="lps",$h[K.m.Ug]="did",$h[K.m.ee]="fcntr",$h[K.m.fe]="flng",$h[K.m.he]="mid",$h[K.m.nf]=void 0,$h[K.m.Eb]="tiba",$h[K.m.Wb]="rdp",$h[K.m.wc]="ecsid",$h[K.m.tf]="ga_uid",$h[K.m.uf]="delopc",$h[K.m.pd]="gdpr_consent",$h[K.m.Xa]="oid",$h[K.m.bl]=
"uptgs",$h[K.m.vf]="uaa",$h[K.m.wf]="uab",$h[K.m.xf]="uafvl",$h[K.m.yf]="uamb",$h[K.m.zf]="uam",$h[K.m.Af]="uap",$h[K.m.Bf]="uapv",$h[K.m.Cf]="uaw",$h[K.m.si]="ec_lat",$h[K.m.ui]="ec_meta",$h[K.m.wi]="ec_m",$h[K.m.xi]="ec_sel",$h[K.m.yi]="ec_s",$h[K.m.yc]="ec_mode",$h[K.m.Qa]="userId",$h[K.m.Df]="us_privacy",$h[K.m.Fa]="value",$h[K.m.il]="mcov",$h[K.m.Ci]="hn",$h[K.m.sl]="gtm_ee",$h[K.m.zc]="npa",$h[K.m.Ze]=null,$h[K.m.Pc]=null,$h[K.m.Ab]=null,$h[K.m.sa]=null,$h[K.m.Aa]=null,$h[K.m.Wa]=null,$h[K.m.oi]=
null,$h[K.m.ud]=null,$h[K.m.Me]=null,$h[K.m.Ne]=null,$h[K.m.uc]=null,$h);function bi(a,b){if(a){var c=a.split("x");c.length===2&&(ci(b,"u_w",c[0]),ci(b,"u_h",c[1]))}}
function di(a){var b=ei;b=b===void 0?fi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(gi(q.value)),r.push(gi(q.quantity)),r.push(gi(q.item_id)),r.push(gi(q.start_date)),r.push(gi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function fi(a){return hi(a.item_id,a.id,a.item_name)}function hi(){for(var a=l(xa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ii(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ci(a,b,c){c===void 0||c===null||c===""&&!Ag[b]||(a[b]=c)}function gi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ji={},li={wq:ki};function mi(a,b){var c=ji[b],d=c.Om;if(!(ji[b].active||ji[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;li.wq(a,b)}}function ki(a,b){var c=ji[b];if(!(pb(0,9999)<c.percent*2*100))return a;ni(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function ni(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=pb(0,1)===0,e=pb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var L={J:{Wj:"call_conversion",W:"conversion",eo:"floodlight",Ff:"ga_conversion",Ki:"landing_page",Ga:"page_view",na:"remarketing",Ta:"user_data_lead",Ja:"user_data_web"}};function qi(a){return ri?A.querySelectorAll(a):null}
function si(a,b){if(!ri)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ti=!1;
if(A.querySelectorAll)try{var ui=A.querySelectorAll(":root");ui&&ui.length==1&&ui[0]==A.documentElement&&(ti=!0)}catch(a){}var ri=ti;function vi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function wi(){this.blockSize=-1};function xi(a,b){this.blockSize=-1;this.blockSize=64;this.N=ya.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=ya.Int32Array?new Int32Array(64):Array(64);yi===void 0&&(ya.Int32Array?yi=new Int32Array(zi):yi=zi);this.reset()}za(xi,wi);for(var Ai=[],Bi=0;Bi<63;Bi++)Ai[Bi]=0;var Ci=[].concat(128,Ai);
xi.prototype.reset=function(){this.P=this.H=0;var a;if(ya.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Di=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(yi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
xi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Di(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Di(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};xi.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Ci,56-this.H):this.update(Ci,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Di(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var zi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],yi;function Ei(){xi.call(this,8,Fi)}za(Ei,xi);var Fi=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Gi=/^[0-9A-Fa-f]{64}$/;function Hi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Ii(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Gi.test(a))return Promise.resolve(a);try{var d=Hi(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Ji(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ji(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ki=[],Li;function Mi(a){Li?Li(a):Ki.push(a)}function Ni(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Mi(a),b):c}function Oi(a,b){if(!E(190))return b;var c=Pi(a,"");return c!==b?(Mi(a),b):c}function Pi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Qi(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Mi(a),b)}function Ri(){Li=Si;for(var a=l(Ki),b=a.next();!b.done;b=a.next())Li(b.value);Ki.length=0};var Ti={jn:'512',kn:'0',ln:'1000',jo:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',ko:'US-CO',Fo:Oi(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104718208~104839054~104839056~104908318~104908320')},Ui={fp:Number(Ti.jn)||0,hp:Number(Ti.kn)||0,kp:Number(Ti.ln)||0,Bp:Ti.jo.split("~"),Cp:Ti.ko.split("~"),Mq:Ti.Fo};Object.assign({},Ui);function M(a){db("GTM",a)};var Fj={},Gj=(Fj[K.m.nb]=1,Fj[K.m.od]=2,Fj[K.m.xc]=2,Fj[K.m.za]=3,Fj[K.m.af]=4,Fj[K.m.Cg]=5,Fj[K.m.Jc]=6,Fj[K.m.eb]=6,Fj[K.m.ob]=6,Fj[K.m.gd]=6,Fj[K.m.Tb]=6,Fj[K.m.zb]=6,Fj[K.m.pb]=7,Fj[K.m.Wb]=9,Fj[K.m.Dg]=10,Fj[K.m.Pb]=11,Fj),Hj={},Ij=(Hj.unknown=13,Hj.standard=14,Hj.unique=15,Hj.per_session=16,Hj.transactions=17,Hj.items_sold=18,Hj);var fb=[];function Jj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Gj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Gj[f],h=b;h=h===void 0?!1:h;db("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(fb[g]=!0)}}};var Kj=function(){this.C=new Set;this.H=new Set},Mj=function(a){var b=Lj.R;a=a===void 0?[]:a;var c=[].concat(ta(b.C)).concat([].concat(ta(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Nj=function(){var a=[].concat(ta(Lj.R.C));a.sort(function(b,c){return b-c});return a},Oj=function(){var a=Lj.R,b=Ui.Mq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Pj={},Qj=Oi(14,"56u2"),Rj=Qi(15,Number("0")),Sj=Oi(19,"dataLayer");Oi(20,"");Oi(16,"ChAI8LGTwwYQx+2Y4ZmlzdxVEicAhIAAVtYnNUr8Xo6kSVhmaIlbP5KSgrvdVUjINL1yuPKusoIDWgsaAm+v");var Tj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Uj={__paused:1,__tg:1},Vj;for(Vj in Tj)Tj.hasOwnProperty(Vj)&&(Uj[Vj]=1);var Wj=Ni(11,vb("")),Xj=!1,Yj,Zj=!1;Zj=!0;
Yj=Zj;var ak,bk=!1;ak=bk;Pj.Ag=Oi(3,"www.googletagmanager.com");var ck=""+Pj.Ag+(Yj?"/gtag/js":"/gtm.js"),dk=null,ek=null,fk={},gk={};Pj.Zm=Ni(2,vb(""));var hk="";Pj.Pi=hk;
var Lj=new function(){this.R=new Kj;this.C=this.N=!1;this.H=0;this.Ba=this.Sa=this.rb=this.P="";this.ba=this.ka=!1};function ik(){var a;a=a===void 0?[]:a;return Mj(a).join("~")}function jk(){var a=Lj.P.length;return Lj.P[a-1]==="/"?Lj.P.substring(0,a-1):Lj.P}function kk(){return Lj.C?E(84)?Lj.H===0:Lj.H!==1:!1}function lk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var mk=new rb,nk={},ok={},rk={name:Sj,set:function(a,b){kd(Gb(a,b),nk);pk()},get:function(a){return qk(a,2)},reset:function(){mk=new rb;nk={};pk()}};function qk(a,b){return b!=2?mk.get(a):sk(a)}function sk(a,b){var c=a.split(".");b=b||[];for(var d=nk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function tk(a,b){ok.hasOwnProperty(a)||(mk.set(a,b),kd(Gb(a,b),nk),pk())}
function uk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=qk(c,1);if(Array.isArray(d)||jd(d))d=kd(d,null);ok[c]=d}}function pk(a){sb(ok,function(b,c){mk.set(b,c);kd(Gb(b),nk);kd(Gb(b,c),nk);a&&delete ok[b]})}function vk(a,b){var c,d=(b===void 0?2:b)!==1?sk(a):mk.get(a);hd(d)==="array"||hd(d)==="object"?c=kd(d,null):c=d;return c};var Fk=/:[0-9]+$/,Gk=/^\d+\.fls\.doubleclick\.net$/;function Hk(a,b,c,d){var e=Ik(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Ik(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=sa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Jk(a){try{return decodeURIComponent(a)}catch(b){}}function Kk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Lk(a.protocol)||Lk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Fk,"").toLowerCase());return Mk(a,b,c,d,e)}
function Mk(a,b,c,d,e){var f,g=Lk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Nk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Fk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||db("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Hk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Lk(a){return a?a.replace(":","").toLowerCase():""}function Nk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Ok={},Pk=0;
function Qk(a){var b=Ok[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||db("TAGGING",1),d="/"+d);var e=c.hostname.replace(Fk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Pk<5&&(Ok[a]=b,Pk++)}return b}function Rk(a,b,c){var d=Qk(a);return Lb(b,d,c)}
function Sk(a){var b=Qk(x.location.href),c=Kk(b,"host",!1);if(c&&c.match(Gk)){var d=Kk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Tk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Uk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Vk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Qk(""+c+b).href}}function Wk(a,b){if(kk()||Lj.N)return Vk(a,b)}
function Xk(){return!!Pj.Pi&&Pj.Pi.split("@@").join("")!=="SGTM_TOKEN"}function Yk(a){for(var b=l([K.m.od,K.m.xc]),c=b.next();!c.done;c=b.next()){var d=N(a,c.value);if(d)return d}}function Zk(a,b,c){c=c===void 0?"":c;if(!kk())return a;var d=b?Tk[a]||"":"";d==="/gs"&&(c="");return""+jk()+d+c}function $k(a){if(!kk())return a;for(var b=l(Uk),c=b.next();!c.done;c=b.next())if(Eb(a,""+jk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function al(a){var b=String(a[ef.Ra]||"").replace(/_/g,"");return Eb(b,"cvt")?"cvt":b}var bl=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var cl={sq:Qi(27,Number("0.005000")),cp:Qi(42,Number("0.010000"))},dl=Math.random(),el=bl||dl<Number(cl.sq),fl=bl||dl>=1-Number(cl.cp);var gl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},hl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var il,jl;a:{for(var kl=["CLOSURE_FLAGS"],ll=ya,ml=0;ml<kl.length;ml++)if(ll=ll[kl[ml]],ll==null){jl=null;break a}jl=ll}var nl=jl&&jl[610401301];il=nl!=null?nl:!1;function ol(){var a=ya.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var pl,ql=ya.navigator;pl=ql?ql.userAgentData||null:null;function rl(a){if(!il||!pl)return!1;for(var b=0;b<pl.brands.length;b++){var c=pl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function sl(a){return ol().indexOf(a)!=-1};function tl(){return il?!!pl&&pl.brands.length>0:!1}function ul(){return tl()?!1:sl("Opera")}function vl(){return sl("Firefox")||sl("FxiOS")}function wl(){return tl()?rl("Chromium"):(sl("Chrome")||sl("CriOS"))&&!(tl()?0:sl("Edge"))||sl("Silk")};var xl=function(a){xl[" "](a);return a};xl[" "]=function(){};var yl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function zl(){return il?!!pl&&!!pl.platform:!1}function Al(){return sl("iPhone")&&!sl("iPod")&&!sl("iPad")}function Bl(){Al()||sl("iPad")||sl("iPod")};ul();tl()||sl("Trident")||sl("MSIE");sl("Edge");!sl("Gecko")||ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")||sl("Trident")||sl("MSIE")||sl("Edge");ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")&&sl("Mobile");zl()||sl("Macintosh");zl()||sl("Windows");(zl()?pl.platform==="Linux":sl("Linux"))||zl()||sl("CrOS");zl()||sl("Android");Al();sl("iPad");sl("iPod");Bl();ol().toLowerCase().indexOf("kaios");var Cl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{xl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Dl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},El=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Fl=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Cl(b.top)?1:2},Gl=function(a){a=a===void 0?document:a;return a.createElement("img")},Hl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Cl(a)&&(b=a);return b};function Il(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Jl(){return Il("join-ad-interest-group")&&jb(rc.joinAdInterestGroup)}
function Kl(a,b,c){var d=Ia[3]===void 0?1:Ia[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ia[2]===void 0?50:Ia[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&zb()-q<(Ia[1]===void 0?6E4:Ia[1])?(db("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ll(f[0]);else{if(n)return db("TAGGING",10),!1}else f.length>=d?Ll(f[0]):n&&Ll(m[0]);Fc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:zb()});return!0}function Ll(a){try{a.parentNode.removeChild(a)}catch(b){}};function Ml(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Nl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};vl();Al()||sl("iPod");sl("iPad");!sl("Android")||wl()||vl()||ul()||sl("Silk");wl();!sl("Safari")||wl()||(tl()?0:sl("Coast"))||ul()||(tl()?0:sl("Edge"))||(tl()?rl("Microsoft Edge"):sl("Edg/"))||(tl()?rl("Opera"):sl("OPR"))||vl()||sl("Silk")||sl("Android")||Bl();var Ol={},Pl=null,Ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Pl){Pl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Ol[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Pl[q]===void 0&&(Pl[q]=p)}}}for(var r=Ol[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
z=b[v+1],B=b[v+2],D=r[y>>2],F=r[(y&3)<<4|z>>4],H=r[(z&15)<<2|B>>6],J=r[B&63];t[w++]=""+D+F+H+J}var S=0,ca=u;switch(b.length-v){case 2:S=b[v+1],ca=r[(S&15)<<2]||u;case 1:var U=b[v];t[w]=""+r[U>>2]+r[(U&3)<<4|S>>4]+ca+u}return t.join("")};var Rl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Sl=/#|$/,Tl=function(a,b){var c=a.search(Sl),d=Rl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return yl(a.slice(d,e!==-1?e:0))},Ul=/[?&]($|#)/,Vl=function(a,b,c){for(var d,e=a.search(Sl),f=0,g,h=[];(g=Rl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Ul,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Wl(a,b,c,d,e,f){var g=Tl(c,"fmt");if(d){var h=Tl(c,"random"),m=Tl(c,"label")||"";if(!h)return!1;var n=Ql(yl(m)+":"+yl(h));if(!Ml(a,n,d))return!1}g&&Number(g)!==4&&(c=Vl(c,"rfmt",g));var p=Vl(c,"fmt",4);Dc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Xl={},Yl=(Xl[1]={},Xl[2]={},Xl[3]={},Xl[4]={},Xl);function Zl(a,b,c){var d=$l(b,c);if(d){var e=Yl[b][d];e||(e=Yl[b][d]=[]);e.push(Object.assign({},a))}}function am(a,b){var c=$l(a,b);if(c){var d=Yl[a][c];d&&(Yl[a][c]=d.filter(function(e){return!e.Im}))}}function bm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function $l(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function cm(a){var b=xa.apply(1,arguments);fl&&(Zl(a,2,b[0]),Zl(a,3,b[0]));Pc.apply(null,ta(b))}function dm(a){var b=xa.apply(1,arguments);fl&&Zl(a,2,b[0]);return Qc.apply(null,ta(b))}function em(a){var b=xa.apply(1,arguments);fl&&Zl(a,3,b[0]);Gc.apply(null,ta(b))}
function fm(a){var b=xa.apply(1,arguments),c=b[0];fl&&(Zl(a,2,c),Zl(a,3,c));return Tc.apply(null,ta(b))}function gm(a){var b=xa.apply(1,arguments);fl&&Zl(a,1,b[0]);Dc.apply(null,ta(b))}function hm(a){var b=xa.apply(1,arguments);b[0]&&fl&&Zl(a,4,b[0]);Fc.apply(null,ta(b))}function im(a){var b=xa.apply(1,arguments);fl&&Zl(a,1,b[2]);return Wl.apply(null,ta(b))}function jm(a){var b=xa.apply(1,arguments);fl&&Zl(a,4,b[0]);Kl.apply(null,ta(b))};var km=/gtag[.\/]js/,lm=/gtm[.\/]js/,mm=!1;function nm(a){if(mm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(km.test(c))return"3";if(lm.test(c))return"2"}return"0"};function om(a,b,c){var d=pm(),e=qm().container[a];e&&e.state!==3||(qm().container[a]={state:1,context:b,parent:d},rm({ctid:a,isDestination:!1},c))}function rm(a,b){var c=qm();c.pending||(c.pending=[]);ob(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function sm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var tm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=sm()};function qm(){var a=vc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new tm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=sm());return c};var um={},ig={ctid:Oi(5,"UA-119892649-1"),canonicalContainerId:Oi(6,""),Bm:Oi(10,"UA-119892649-1"),Cm:Oi(9,"UA-119892649-1")};um.qe=Ni(7,vb(""));function vm(){return um.qe&&wm().some(function(a){return a===ig.ctid})}function xm(){return ig.canonicalContainerId||"_"+ig.ctid}function ym(){return ig.Bm?ig.Bm.split("|"):[ig.ctid]}
function wm(){return ig.Cm?ig.Cm.split("|").filter(function(a){return E(108)?a.indexOf("GTM-")!==0:!0}):[]}function zm(){var a=Am(pm()),b=a&&a.parent;if(b)return Am(b)}function Bm(){var a=Am(pm());if(a){for(;a.parent;){var b=Am(a.parent);if(!b)break;a=b}return a}}function Am(a){var b=qm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Cm(){var a=qm();if(a.pending){for(var b,c=[],d=!1,e=ym(),f=wm(),g={},h=0;h<a.pending.length;g={mg:void 0},h++)g.mg=a.pending[h],ob(g.mg.target.isDestination?f:e,function(m){return function(n){return n===m.mg.target.ctid}}(g))?d||(b=g.mg.onLoad,d=!0):c.push(g.mg);a.pending=c;if(b)try{b(xm())}catch(m){}}}
function Dm(){for(var a=ig.ctid,b=ym(),c=wm(),d=function(n,p){var q={canonicalContainerId:ig.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};tc&&(q.scriptElement=tc);uc&&(q.scriptSource=uc);if(zm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Lj.C,y=Qk(v),z=w?y.pathname:""+y.hostname+y.pathname,B=A.scripts,D="",F=0;F<B.length;++F){var H=B[F];if(!(H.innerHTML.length===
0||!w&&H.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||H.innerHTML.indexOf(z)<0)){if(H.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}D=String(F)}}if(D){t=D;break b}}t=void 0}var J=t;if(J){mm=!0;r=J;break a}}var S=[].slice.call(A.scripts);r=q.scriptElement?String(S.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=nm(q)}var ca=p?e.destination:e.container,U=ca[n];U?(p&&U.state===0&&M(93),Object.assign(U,q)):ca[n]=q},e=qm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[xm()]={};Cm()}function Em(){var a=xm();return!!qm().canonical[a]}function Fm(a){return!!qm().container[a]}function Gm(a){var b=qm().destination[a];return!!b&&!!b.state}function pm(){return{ctid:ig.ctid,isDestination:um.qe}}function Hm(){var a=qm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Im(){var a={};sb(qm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Jm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Km(){for(var a=qm(),b=l(ym()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Lm={Ia:{je:0,pe:1,Li:2}};Lm.Ia[Lm.Ia.je]="FULL_TRANSMISSION";Lm.Ia[Lm.Ia.pe]="LIMITED_TRANSMISSION";Lm.Ia[Lm.Ia.Li]="NO_TRANSMISSION";var Mm={X:{Gb:0,Da:1,Hc:2,Qc:3}};Mm.X[Mm.X.Gb]="NO_QUEUE";Mm.X[Mm.X.Da]="ADS";Mm.X[Mm.X.Hc]="ANALYTICS";Mm.X[Mm.X.Qc]="MONITORING";function Nm(){var a=vc("google_tag_data",{});return a.ics=a.ics||new Om}var Om=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Om.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;db("TAGGING",19);b==null?db("TAGGING",18):Pm(this,a,b==="granted",c,d,e,f,g)};Om.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Pm(this,a[d],void 0,void 0,"","",b,c)};
var Pm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&lb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(db("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Om.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Qm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Qm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&lb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Ae:b})};var Qm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Dm=!0)}};Om.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Dm){d.Dm=!1;try{d.Ae({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Rm=!1,Sm=!1,Tm={},Um={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Tm.ad_storage=1,Tm.analytics_storage=1,Tm.ad_user_data=1,Tm.ad_personalization=1,Tm),usedContainerScopedDefaults:!1};function Vm(a){var b=Nm();b.accessedAny=!0;return(lb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Um)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Wm(a){var b=Nm();b.accessedAny=!0;return b.getConsentState(a,Um)}function Xm(a){var b=Nm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function Ym(){if(!Ja(8))return!1;var a=Nm();a.accessedAny=!0;if(a.active)return!0;if(!Um.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Um.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Um.containerScopedDefaults[c.value]!==1)return!0;return!1}function Zm(a,b){Nm().addListener(a,b)}
function $m(a,b){Nm().notifyListeners(a,b)}function an(a,b){function c(){for(var e=0;e<b.length;e++)if(!Xm(b[e]))return!0;return!1}if(c()){var d=!1;Zm(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function bn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Vm(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=lb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Zm(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var cn={},dn=(cn[Mm.X.Gb]=Lm.Ia.je,cn[Mm.X.Da]=Lm.Ia.je,cn[Mm.X.Hc]=Lm.Ia.je,cn[Mm.X.Qc]=Lm.Ia.je,cn),en=function(a,b){this.C=a;this.consentTypes=b};en.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Vm(a)});case 1:return this.consentTypes.some(function(a){return Vm(a)});default:jc(this.C,"consentsRequired had an unknown type")}};
var fn={},gn=(fn[Mm.X.Gb]=new en(0,[]),fn[Mm.X.Da]=new en(0,["ad_storage"]),fn[Mm.X.Hc]=new en(0,["analytics_storage"]),fn[Mm.X.Qc]=new en(1,["ad_storage","analytics_storage"]),fn);var jn=function(a){var b=this;this.type=a;this.C=[];Zm(gn[a].consentTypes,function(){hn(b)||b.flush()})};jn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var hn=function(a){return dn[a.type]===Lm.Ia.Li&&!gn[a.type].isConsentGranted()},kn=function(a,b){hn(a)?a.C.push(b):b()},ln=new Map;function mn(a){ln.has(a)||ln.set(a,new jn(a));return ln.get(a)};var nn={Z:{Wm:"aw_user_data_cache",Ph:"cookie_deprecation_label",Bg:"diagnostics_page_id",fo:"fl_user_data_cache",io:"ga4_user_data_cache",Gf:"ip_geo_data_cache",Fi:"ip_geo_fetch_in_progress",Cl:"nb_data",yo:"page_experiment_ids",Of:"pt_data",El:"pt_listener_set",Ll:"service_worker_endpoint",Nl:"shared_user_id",Ol:"shared_user_id_requested",ph:"shared_user_id_source"}};var on=function(a){return Ye(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(nn.Z);
function pn(a,b){b=b===void 0?!1:b;if(on(a)){var c,d,e=(d=(c=vc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function qn(a,b){var c=pn(a,!0);c&&c.set(b)}function rn(a){var b;return(b=pn(a))==null?void 0:b.get()}function sn(a){var b={},c=pn(a);if(!c){c=pn(a,!0);if(!c)return;c.set(b)}return c.get()}function tn(a,b){if(typeof b==="function"){var c;return(c=pn(a,!0))==null?void 0:c.subscribe(b)}}function un(a,b){var c=pn(a);return c?c.unsubscribe(b):!1};var vn="https://"+Oi(21,"www.googletagmanager.com"),wn="/td?id="+ig.ctid,xn={},yn=(xn.tdp=1,xn.exp=1,xn.pid=1,xn.dl=1,xn.seq=1,xn.t=1,xn.v=1,xn),zn=["mcc"],An={},Bn={},Cn=!1,Dn=void 0;function En(a,b,c){Bn[a]=b;(c===void 0||c)&&Fn(a)}function Fn(a,b){An[a]!==void 0&&(b===void 0||!b)||Eb(ig.ctid,"GTM-")&&a==="mcc"||(An[a]=!0)}
function Gn(a){a=a===void 0?!1:a;var b=Object.keys(An).filter(function(c){return An[c]===!0&&Bn[c]!==void 0&&(a||!zn.includes(c))}).map(function(c){var d=Bn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Zk(vn)+wn+(""+b+"&z=0")}function Hn(){Object.keys(An).forEach(function(a){yn[a]||(An[a]=!1)})}
function In(a){a=a===void 0?!1:a;if(Lj.ba&&fl&&ig.ctid){var b=mn(Mm.X.Qc);if(hn(b))Cn||(Cn=!0,kn(b,In));else{var c=Gn(a),d={destinationId:ig.ctid,endpoint:61};a?fm(d,c,void 0,{Hh:!0},void 0,function(){em(d,c+"&img=1")}):em(d,c);Hn();Cn=!1}}}var Jn={};
function Kn(a){var b=String(a);Jn.hasOwnProperty(b)||(Jn[b]=!0,En("csp",Object.keys(Jn).join("~")),Fn("csp",!0),Dn===void 0&&E(171)&&(Dn=x.setTimeout(function(){var c=An.csp;An.csp=!0;An.seq=!1;var d=Gn(!1);An.csp=c;An.seq=!0;Dc(d+"&script=1");Dn=void 0},500)))}function Ln(){Object.keys(An).filter(function(a){return An[a]&&!yn[a]}).length>0&&In(!0)}var Mn;
function Nn(){if(rn(nn.Z.Bg)===void 0){var a=function(){qn(nn.Z.Bg,pb());Mn=0};a();x.setInterval(a,864E5)}else tn(nn.Z.Bg,function(){Mn=0});Mn=0}function On(){Nn();En("v","3");En("t","t");En("pid",function(){return String(rn(nn.Z.Bg))});En("seq",function(){return String(++Mn)});En("exp",ik());Ic(x,"pagehide",Ln)};var Pn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Qn=[K.m.od,K.m.xc,K.m.Zd,K.m.Rb,K.m.wc,K.m.Qa,K.m.Pa,K.m.eb,K.m.ob,K.m.Tb],Rn=!1,Sn=!1,Tn={},Un={};function Vn(){!Sn&&Rn&&(Pn.some(function(a){return Um.containerScopedDefaults[a]!==1})||Wn("mbc"));Sn=!0}function Wn(a){fl&&(En(a,"1"),In())}function Xn(a,b){if(!Tn[b]&&(Tn[b]=!0,Un[b]))for(var c=l(Qn),d=c.next();!d.done;d=c.next())if(N(a,d.value)){Wn("erc");break}};function Yn(a){db("HEALTH",a)};var Zn={wp:Oi(22,"eyIwIjoiSUwiLCIxIjoiSUwtSEEiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jby5pbCIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9")},$n={},ao=!1;function bo(){function a(){c!==void 0&&un(nn.Z.Gf,c);try{var e=rn(nn.Z.Gf);$n=JSON.parse(e)}catch(f){M(123),Yn(2),$n={}}ao=!0;b()}var b=co,c=void 0,d=rn(nn.Z.Gf);d?a(d):(c=tn(nn.Z.Gf,a),eo())}
function eo(){function a(c){qn(nn.Z.Gf,c||"{}");qn(nn.Z.Fi,!1)}if(!rn(nn.Z.Fi)){qn(nn.Z.Fi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function fo(){var a=Zn.wp;try{return JSON.parse(bb(a))}catch(b){return M(123),Yn(2),{}}}function go(){return $n["0"]||""}function ho(){return $n["1"]||""}function io(){var a=!1;return a}function jo(){return $n["6"]!==!1}function ko(){var a="";return a}
function lo(){var a=!1;a=!!$n["5"];return a}function mo(){var a="";return a};var no={},oo=Object.freeze((no[K.m.Ea]=1,no[K.m.Dg]=1,no[K.m.Eg]=1,no[K.m.Pb]=1,no[K.m.sa]=1,no[K.m.ob]=1,no[K.m.pb]=1,no[K.m.zb]=1,no[K.m.gd]=1,no[K.m.Tb]=1,no[K.m.eb]=1,no[K.m.Jc]=1,no[K.m.bf]=1,no[K.m.oa]=1,no[K.m.wk]=1,no[K.m.ef]=1,no[K.m.Og]=1,no[K.m.Pg]=1,no[K.m.Zd]=1,no[K.m.Lk]=1,no[K.m.uc]=1,no[K.m.ce]=1,no[K.m.Nk]=1,no[K.m.Sg]=1,no[K.m.ji]=1,no[K.m.Mc]=1,no[K.m.Nc]=1,no[K.m.Pa]=1,no[K.m.ki]=1,no[K.m.Wb]=1,no[K.m.qb]=1,no[K.m.nd]=1,no[K.m.od]=1,no[K.m.rf]=1,no[K.m.mi]=1,no[K.m.uf]=1,no[K.m.xc]=
1,no[K.m.rd]=1,no[K.m.Zg]=1,no[K.m.Xb]=1,no[K.m.ud]=1,no[K.m.Oi]=1,no));Object.freeze([K.m.Aa,K.m.Wa,K.m.Eb,K.m.Ab,K.m.li,K.m.Qa,K.m.gi,K.m.In]);
var po={},qo=Object.freeze((po[K.m.mn]=1,po[K.m.nn]=1,po[K.m.on]=1,po[K.m.pn]=1,po[K.m.qn]=1,po[K.m.tn]=1,po[K.m.un]=1,po[K.m.vn]=1,po[K.m.xn]=1,po[K.m.Td]=1,po)),ro={},so=Object.freeze((ro[K.m.mk]=1,ro[K.m.nk]=1,ro[K.m.Pd]=1,ro[K.m.Qd]=1,ro[K.m.pk]=1,ro[K.m.Zc]=1,ro[K.m.Rd]=1,ro[K.m.mc]=1,ro[K.m.Ic]=1,ro[K.m.nc]=1,ro[K.m.lb]=1,ro[K.m.Sd]=1,ro[K.m.yb]=1,ro[K.m.qk]=1,ro)),to=Object.freeze([K.m.Ea,K.m.Re,K.m.Pb,K.m.Jc,K.m.Zd,K.m.lf,K.m.qb,K.m.rd]),uo=Object.freeze([].concat(ta(to))),vo=Object.freeze([K.m.pb,
K.m.Pg,K.m.rf,K.m.mi,K.m.Lg]),wo=Object.freeze([].concat(ta(vo))),xo={},yo=(xo[K.m.U]="1",xo[K.m.ia]="2",xo[K.m.V]="3",xo[K.m.La]="4",xo),zo={},Ao=Object.freeze((zo.search="s",zo.youtube="y",zo.playstore="p",zo.shopping="h",zo.ads="a",zo.maps="m",zo));function Bo(a){return typeof a!=="object"||a===null?{}:a}function Co(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Do(a){if(a!==void 0&&a!==null)return Co(a)}function Eo(a){return typeof a==="number"?a:Do(a)};function Fo(a){return a&&a.indexOf("pending:")===0?Go(a.substr(8)):!1}function Go(a){if(a==null||a.length===0)return!1;var b=Number(a),c=zb();return b<c+3E5&&b>c-9E5};var Ho=!1,Io=!1,Jo=!1,Ko=0,Lo=!1,Mo=[];function No(a){if(Ko===0)Lo&&Mo&&(Mo.length>=100&&Mo.shift(),Mo.push(a));else if(Oo()){var b=Oi(41,'google.tagmanager.ta.prodqueue'),c=vc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Po(){Qo();Jc(A,"TAProdDebugSignal",Po)}function Qo(){if(!Io){Io=!0;Ro();var a=Mo;Mo=void 0;a==null||a.forEach(function(b){No(b)})}}
function Ro(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Go(a)?Ko=1:!Fo(a)||Ho||Jo?Ko=2:(Jo=!0,Ic(A,"TAProdDebugSignal",Po,!1),x.setTimeout(function(){Qo();Ho=!0},200))}function Oo(){if(!Lo)return!1;switch(Ko){case 1:case 0:return!0;case 2:return!1;default:return!1}};var So=!1;function To(a,b){var c=ym(),d=wm();if(Oo()){var e=Uo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;No(e)}}
function Vo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=Oo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=Uo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);No(h)}}function Wo(a){Oo()&&Vo(a())}
function Uo(a,b){b=b===void 0?{}:b;b.groupId=Xo;var c,d=b,e={publicId:Yo};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=So?"OGT":"GTM";c.key.targetRef=Zo;return c}var Yo="",Zo={ctid:"",isDestination:!1},Xo;
function $o(a){var b=ig.ctid,c=vm();Ko=0;Lo=!0;Ro();Xo=a;Yo=b;So=Yj;Zo={ctid:b,isDestination:c}};var ap=[K.m.U,K.m.ia,K.m.V,K.m.La],bp,cp;function dp(a){var b=a[K.m.jc];b||(b=[""]);for(var c={dg:0};c.dg<b.length;c={dg:c.dg},++c.dg)sb(a,function(d){return function(e,f){if(e!==K.m.jc){var g=Co(f),h=b[d.dg],m=go(),n=ho();Sm=!0;Rm&&db("TAGGING",20);Nm().declare(e,g,h,m,n)}}}(c))}
function ep(a){Vn();!cp&&bp&&Wn("crc");cp=!0;var b=a[K.m.vg];b&&M(41);var c=a[K.m.jc];c?M(40):c=[""];for(var d={eg:0};d.eg<c.length;d={eg:d.eg},++d.eg)sb(a,function(e){return function(f,g){if(f!==K.m.jc&&f!==K.m.vg){var h=Do(g),m=c[e.eg],n=Number(b),p=go(),q=ho();n=n===void 0?0:n;Rm=!0;Sm&&db("TAGGING",20);Nm().default(f,h,m,p,q,n,Um)}}}(d))}
function fp(a){Um.usedContainerScopedDefaults=!0;var b=a[K.m.jc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ho())&&!c.includes(go()))return}sb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Um.usedContainerScopedDefaults=!0;Um.containerScopedDefaults[d]=e==="granted"?3:2})}
function gp(a,b){Vn();bp=!0;sb(a,function(c,d){var e=Co(d);Rm=!0;Sm&&db("TAGGING",20);Nm().update(c,e,Um)});$m(b.eventId,b.priorityId)}function hp(a){a.hasOwnProperty("all")&&(Um.selectedAllCorePlatformServices=!0,sb(Ao,function(b){Um.corePlatformServices[b]=a.all==="granted";Um.usedCorePlatformServices=!0}));sb(a,function(b,c){b!=="all"&&(Um.corePlatformServices[b]=c==="granted",Um.usedCorePlatformServices=!0)})}function ip(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Vm(b)})}
function jp(a,b){Zm(a,b)}function kp(a,b){bn(a,b)}function lp(a,b){an(a,b)}function mp(){var a=[K.m.U,K.m.La,K.m.V];Nm().waitForUpdate(a,500,Um)}function np(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Nm().clearTimeout(d,void 0,Um)}$m()}function op(){if(!ak)for(var a=jo()?lk(Lj.Sa):lk(Lj.rb),b=0;b<ap.length;b++){var c=ap[b],d=c,e=a[c]?"granted":"denied";Nm().implicit(d,e)}};var pp=!1,qp=[];function rp(){if(!pp){pp=!0;for(var a=qp.length-1;a>=0;a--)qp[a]();qp=[]}};var sp=x.google_tag_manager=x.google_tag_manager||{};function tp(a,b){return sp[a]=sp[a]||b()}function up(){var a=ig.ctid,b=vp;sp[a]=sp[a]||b}function wp(){var a=sp.sequence||1;sp.sequence=a+1;return a};function xp(){if(sp.pscdl!==void 0)rn(nn.Z.Ph)===void 0&&qn(nn.Z.Ph,sp.pscdl);else{var a=function(c){sp.pscdl=c;qn(nn.Z.Ph,c)},b=function(){a("error")};try{rc.cookieDeprecationLabel?(a("pending"),rc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var yp=0;function zp(a){fl&&a===void 0&&yp===0&&(En("mcc","1"),yp=1)};var Ap={Ef:{bn:"cd",dn:"ce",fn:"cf",gn:"cpf",hn:"cu"}};var Bp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Cp=/\s/;
function Dp(a,b){if(lb(a)){a=xb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Bp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Cp.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Ep(a,b){for(var c={},d=0;d<a.length;++d){var e=Dp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Fp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Gp={},Fp=(Gp[0]=0,Gp[1]=1,Gp[2]=2,Gp[3]=0,Gp[4]=1,Gp[5]=0,Gp[6]=0,Gp[7]=0,Gp);var Hp=Number('')||500,Ip={},Jp={},Kp={initialized:11,complete:12,interactive:13},Lp={},Mp=Object.freeze((Lp[K.m.qb]=!0,Lp)),Np=void 0;function Op(a,b){if(b.length&&fl){var c;(c=Ip)[a]!=null||(c[a]=[]);Jp[a]!=null||(Jp[a]=[]);var d=b.filter(function(e){return!Jp[a].includes(e)});Ip[a].push.apply(Ip[a],ta(d));Jp[a].push.apply(Jp[a],ta(d));!Np&&d.length>0&&(Fn("tdc",!0),Np=x.setTimeout(function(){In();Ip={};Np=void 0},Hp))}}
function Pp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Qp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;hd(t)==="object"?u=t[r]:hd(t)==="array"&&(u=t[r]);return u===void 0?Mp[r]:u},f=Pp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=hd(m)==="object"||hd(m)==="array",q=hd(n)==="object"||hd(n)==="array";if(p&&q)Qp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Rp(){En("tdc",function(){Np&&(x.clearTimeout(Np),Np=void 0);var a=[],b;for(b in Ip)Ip.hasOwnProperty(b)&&a.push(b+"*"+Ip[b].join("."));return a.length?a.join("!"):void 0},!1)};var Sp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Tp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},N=function(a,b,c,d){for(var e=l(Tp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Up=function(a){for(var b={},c=Tp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Sp.prototype.getMergedValues=function(a,b,c){function d(n){jd(n)&&sb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Tp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Vp=function(a){for(var b=[K.m.We,K.m.Se,K.m.Te,K.m.Ue,K.m.Ve,K.m.Xe,K.m.Ye],c=Tp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Wp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Xp=function(a,
b){a.H=b;return a},Yp=function(a,b){a.R=b;return a},Zp=function(a,b){a.C=b;return a},$p=function(a,b){a.N=b;return a},aq=function(a,b){a.ba=b;return a},bq=function(a,b){a.P=b;return a},cq=function(a,b){a.eventMetadata=b||{};return a},dq=function(a,b){a.onSuccess=b;return a},eq=function(a,b){a.onFailure=b;return a},fq=function(a,b){a.isGtmEvent=b;return a},gq=function(a){return new Sp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var O={A:{Tj:"accept_by_default",ug:"add_tag_timing",Lh:"allow_ad_personalization",Vj:"batch_on_navigation",Xj:"client_id_source",Ie:"consent_event_id",Je:"consent_priority_id",Oq:"consent_state",ja:"consent_updated",Yc:"conversion_linker_enabled",ya:"cookie_options",xg:"create_dc_join",yg:"create_fpm_geo_join",zg:"create_fpm_signals_join",Od:"create_google_join",Le:"em_event",Rq:"endpoint_for_debug",lk:"enhanced_client_id_source",Sh:"enhanced_match_result",ie:"euid_mode_enabled",hb:"event_start_timestamp_ms",
nl:"event_usage",bh:"extra_tag_experiment_ids",Yq:"add_parameter",Ai:"attribution_reporting_experiment",Bi:"counting_method",eh:"send_as_iframe",Zq:"parameter_order",fh:"parsed_target",ho:"ga4_collection_subdomain",ql:"gbraid_cookie_marked",fa:"hit_type",vd:"hit_type_override",mo:"is_config_command",Hf:"is_consent_update",If:"is_conversion",vl:"is_ecommerce",wd:"is_external_event",Gi:"is_fallback_aw_conversion_ping_allowed",Jf:"is_first_visit",wl:"is_first_visit_conversion",gh:"is_fl_fallback_conversion_flow_allowed",
ke:"is_fpm_encryption",hh:"is_fpm_split",me:"is_gcp_conversion",Hi:"is_google_signals_allowed",xd:"is_merchant_center",xl:"is_new_join_id_required",ih:"is_new_to_site",jh:"is_server_side_destination",ne:"is_session_start",zl:"is_session_start_conversion",gr:"is_sgtm_ga_ads_conversion_study_control_group",hr:"is_sgtm_prehit",Al:"is_sgtm_service_worker",Ii:"is_split_conversion",no:"is_syn",oe:"join_id",Ji:"join_elapsed",Kf:"join_timer_sec",se:"tunnel_updated",lr:"prehit_for_retry",nr:"promises",qr:"record_aw_latency",
Ac:"redact_ads_data",te:"redact_click_ids",zo:"remarketing_only",Jl:"send_ccm_parallel_ping",oh:"send_fledge_experiment",ur:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Ni:"send_to_targets",Kl:"send_user_data_hit",ib:"source_canonical_id",Ha:"speculative",Pl:"speculative_in_message",Ql:"suppress_script_load",Rl:"syn_or_mod",Vl:"transient_ecsid",Qf:"transmission_type",jb:"user_data",xr:"user_data_from_automatic",yr:"user_data_from_automatic_getter",ve:"user_data_from_code",rh:"user_data_from_manual",
Xl:"user_data_mode",Rf:"user_id_updated"}};var hq={Vm:Number("5"),Pr:Number("")},iq=[],jq=!1;function kq(a){iq.push(a)}var lq="?id="+ig.ctid,mq=void 0,nq={},oq=void 0,pq=new function(){var a=5;hq.Vm>0&&(a=hq.Vm);this.H=a;this.C=0;this.N=[]},qq=1E3;
function rq(a,b){var c=mq;if(c===void 0)if(b)c=wp();else return"";for(var d=[Zk("https://www.googletagmanager.com"),"/a",lq],e=l(iq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function sq(){if(Lj.ba&&(oq&&(x.clearTimeout(oq),oq=void 0),mq!==void 0&&tq)){var a=mn(Mm.X.Qc);if(hn(a))jq||(jq=!0,kn(a,sq));else{var b;if(!(b=nq[mq])){var c=pq;b=c.C<c.H?!1:zb()-c.N[c.C%c.H]<1E3}if(b||qq--<=0)M(1),nq[mq]=!0;else{var d=pq,e=d.C++%d.H;d.N[e]=zb();var f=rq(!0);em({destinationId:ig.ctid,endpoint:56,eventId:mq},f);jq=tq=!1}}}}function uq(){if(el&&Lj.ba){var a=rq(!0,!0);em({destinationId:ig.ctid,endpoint:56,eventId:mq},a)}}var tq=!1;
function vq(a){nq[a]||(a!==mq&&(sq(),mq=a),tq=!0,oq||(oq=x.setTimeout(sq,500)),rq().length>=2022&&sq())}var wq=pb();function xq(){wq=pb()}function yq(){return[["v","3"],["t","t"],["pid",String(wq)]]};var zq={};function Aq(a,b,c){el&&a!==void 0&&(zq[a]=zq[a]||[],zq[a].push(c+b),vq(a))}function Bq(a){var b=a.eventId,c=a.Nd,d=[],e=zq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete zq[b];return d};function Cq(a,b,c,d){var e=Dp(a,!0);e&&Dq.register(e,b,c,d)}function Eq(a,b,c,d){var e=Dp(c,d.isGtmEvent);e&&(Xj&&(d.deferrable=!0),Dq.push("event",[b,a],e,d))}function Fq(a,b,c,d){var e=Dp(c,d.isGtmEvent);e&&Dq.push("get",[a,b],e,d)}function Gq(a){var b=Dp(a,!0),c;b?c=Hq(Dq,b).C:c={};return c}function Iq(a,b){var c=Dp(a,!0);c&&Jq(Dq,c,b)}
var Kq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Lq=function(a,b,c,d){this.H=zb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Mq=function(){this.destinations={};this.C={};this.commands=[]},Hq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Kq},Nq=function(a,b,c,d){if(d.C){var e=Hq(a,d.C),f=e.ba;if(f){var g=kd(c,null),h=kd(e.R[d.C.id],null),m=kd(e.P,null),n=kd(e.C,null),p=kd(a.C,null),q={};if(el)try{q=
kd(nk,null)}catch(w){M(72)}var r=d.C.prefix,t=function(w){Aq(d.messageContext.eventId,r,w)},u=gq(fq(eq(dq(cq(aq($p(bq(Zp(Yp(Xp(new Wp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Aq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(fl&&w==="config"){var z,B=(z=Dp(y))==null?void 0:z.ids;if(!(B&&B.length>1)){var D,F=vc("google_tag_data",{});F.td||(F.td={});D=F.td;var H=kd(u.P);kd(u.C,H);var J=[],S;for(S in D)D.hasOwnProperty(S)&&Qp(D[S],H).length&&J.push(S);J.length&&(Op(y,J),db("TAGGING",Kp[A.readyState]||14));D[y]=H}}f(d.C.id,b,d.H,u)}catch(ca){Aq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():kn(e.ka,v)}}};
Mq.prototype.register=function(a,b,c,d){var e=Hq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=mn(c),Jq(this,a,d||{}),this.flush())};
Mq.prototype.push=function(a,b,c,d){c!==void 0&&(Hq(this,c).status===1&&(Hq(this,c).status=2,this.push("require",[{}],c,{})),Hq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[O.A.Pf]||(d.eventMetadata[O.A.Pf]=[c.destinationId]),d.eventMetadata[O.A.Ni]||(d.eventMetadata[O.A.Ni]=[c.id]));this.commands.push(new Lq(a,c,b,d));d.deferrable||this.flush()};
Mq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Sc:void 0,xh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Hq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Hq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];sb(h,function(t,u){kd(Gb(t,u),b.C)});Jj(h,!0);break;case "config":var m=Hq(this,g);
e.Sc={};sb(f.args[0],function(t){return function(u,v){kd(Gb(u,v),t.Sc)}}(e));var n=!!e.Sc[K.m.rd];delete e.Sc[K.m.rd];var p=g.destinationId===g.id;Jj(e.Sc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Nq(this,K.m.qa,e.Sc,f);m.N=!0;p?kd(e.Sc,m.P):(kd(e.Sc,m.R[g.id]),M(70));d=!0;break;case "event":e.xh={};sb(f.args[0],function(t){return function(u,v){kd(Gb(u,v),t.xh)}}(e));Jj(e.xh);Nq(this,f.args[1],e.xh,f);break;case "get":var q={},r=(q[K.m.sc]=f.args[0],q[K.m.Kc]=f.args[1],q);Nq(this,K.m.Db,r,f)}this.commands.shift();
Oq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Oq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Hq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Jq=function(a,b,c){var d=kd(c,null);kd(Hq(a,b).C,d);Hq(a,b).C=d},Dq=new Mq;function Pq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Qq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Rq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Gl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=oc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Qq(e,"load",f);Qq(e,"error",f)};Pq(e,"load",f);Pq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Sq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Dl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Tq(c,b)}
function Tq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Rq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Uq=function(){this.ba=this.ba;this.P=this.P};Uq.prototype.ba=!1;Uq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Uq.prototype[Symbol.dispose]=function(){this.dispose()};Uq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Uq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Vq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Wq=function(a,b){b=b===void 0?{}:b;Uq.call(this);this.C=null;this.ka={};this.rb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Er)!=null?d:!1};ra(Wq,Uq);Wq.prototype.N=function(){this.ka={};this.R&&(Qq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Uq.prototype.N.call(this)};var Yq=function(a){return typeof a.H.__tcfapi==="function"||Xq(a)!=null};
Wq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=hl(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Vq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{Zq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Wq.prototype.removeEventListener=function(a){a&&a.listenerId&&Zq(this,"removeEventListener",null,a.listenerId)};
var ar=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=$q(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&$q(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?$q(a.purpose.legitimateInterests,
b)&&$q(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},$q=function(a,b){return!(!a||!a[b])},Zq=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Xq(a)){br(a);var g=++a.rb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Xq=function(a){if(a.C)return a.C;a.C=El(a.H,"__tcfapiLocator");return a.C},br=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Pq(a.H,"message",b)}},cr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Vq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Sq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var dr={1:0,3:0,4:0,7:3,9:3,10:3};function er(){return tp("tcf",function(){return{}})}var fr=function(){return new Wq(x,{timeoutMs:-1})};
function gr(){var a=er(),b=fr();Yq(b)&&!hr()&&!ir()&&M(124);if(!a.active&&Yq(b)){hr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Nm().active=!0,a.tcString="tcunavailable");mp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)jr(a),np([K.m.U,K.m.La,K.m.V]),Nm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,ir()&&(a.active=!0),!kr(c)||hr()||ir()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in dr)dr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(kr(c)){var g={},h;for(h in dr)if(dr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={vp:!0};p=p===void 0?{}:p;m=cr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.vp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?ar(n,"1",0):!0:!1;g["1"]=m}else g[h]=ar(c,h,dr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(np([K.m.U,K.m.La,K.m.V]),Nm().active=!0):(r[K.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":np([K.m.V]),gp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:lr()||""}))}}else np([K.m.U,K.m.La,K.m.V])})}catch(c){jr(a),np([K.m.U,K.m.La,K.m.V]),Nm().active=!0}}}
function jr(a){a.type="e";a.tcString="tcunavailable"}function kr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function hr(){return x.gtag_enable_tcf_support===!0}function ir(){return er().enableAdvertiserConsentMode===!0}function lr(){var a=er();if(a.active)return a.tcString}function mr(){var a=er();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function nr(a){if(!dr.hasOwnProperty(String(a)))return!0;var b=er();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var or=[K.m.U,K.m.ia,K.m.V,K.m.La],pr={},qr=(pr[K.m.U]=1,pr[K.m.ia]=2,pr);function rr(a){if(a===void 0)return 0;switch(N(a,K.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function sr(){return(E(183)?Ui.Bp:Ui.Cp).indexOf(ho())!==-1&&rc.globalPrivacyControl===!0}function tr(a){if(sr())return!1;var b=rr(a);if(b===3)return!1;switch(Wm(K.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function ur(){return Ym()||!Vm(K.m.U)||!Vm(K.m.ia)}function vr(){var a={},b;for(b in qr)qr.hasOwnProperty(b)&&(a[qr[b]]=Wm(b));return"G1"+af(a[1]||0)+af(a[2]||0)}var wr={},xr=(wr[K.m.U]=0,wr[K.m.ia]=1,wr[K.m.V]=2,wr[K.m.La]=3,wr);function yr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function zr(a){for(var b="1",c=0;c<or.length;c++){var d=b,e,f=or[c],g=Um.delegatedConsentTypes[f];e=g===void 0?0:xr.hasOwnProperty(g)?12|xr[g]:8;var h=Nm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|yr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[yr(m.declare)<<4|yr(m.default)<<2|yr(m.update)])}var n=b,p=(sr()?1:0)<<3,q=(Ym()?1:0)<<2,r=rr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Um.containerScopedDefaults.ad_storage<<4|Um.containerScopedDefaults.analytics_storage<<2|Um.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Um.usedContainerScopedDefaults?1:0)<<2|Um.containerScopedDefaults.ad_personalization]}
function Ar(){if(!Vm(K.m.V))return"-";for(var a=Object.keys(Ao),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Um.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Ao[m])}(Um.usedCorePlatformServices?Um.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Br(){return jo()||(hr()||ir())&&mr()==="1"?"1":"0"}function Cr(){return(jo()?!0:!(!hr()&&!ir())&&mr()==="1")||!Vm(K.m.V)}
function Dr(){var a="0",b="0",c;var d=er();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=er();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;jo()&&(h|=1);mr()==="1"&&(h|=2);hr()&&(h|=4);var m;var n=er();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Nm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Er(){return ho()==="US-CO"};var eg;function Fr(){var a=!1;return a}function Gr(){E(212)&&Yj&&fg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};var Hr;function Ir(){if(uc===null)return 0;var a=Zc();if(!a)return 0;var b=a.getEntriesByName(uc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Jr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Kr(a){a=a===void 0?{}:a;var b=ig.ctid.split("-")[0].toUpperCase(),c={ctid:ig.ctid,Jj:Rj,Nj:Qj,rm:um.qe?2:1,Dq:a.Lm,xe:ig.canonicalContainerId};if(E(210)){var d;c.tq=(d=Bm())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.Ro=(e=Hr)!=null?e:Hr=Ir()}c.xe!==a.Ma&&(c.Ma=a.Ma);var f=zm();c.Am=f?f.canonicalContainerId:void 0;Yj?(c.Wc=Jr[b],c.Wc||(c.Wc=0)):c.Wc=ak?13:10;Lj.C?(c.Uc=0,c.bm=2):Lj.N?c.Uc=1:Fr()?c.Uc=2:c.Uc=3;var g={6:!1};Lj.H===2?g[7]=!0:Lj.H===1&&(g[2]=!0);if(uc){var h=Kk(Qk(uc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.fm=g;return df(c,a.th)}
function Lr(){if(!E(192))return Kr();if(E(193))return df({Jj:Rj,Nj:Qj});var a=ig.ctid.split("-")[0].toUpperCase(),b={ctid:ig.ctid,Jj:Rj,Nj:Qj,rm:um.qe?2:1,xe:ig.canonicalContainerId},c=zm();b.Am=c?c.canonicalContainerId:void 0;Yj?(b.Wc=Jr[a],b.Wc||(b.Wc=0)):b.Wc=ak?13:10;Lj.C?(b.Uc=0,b.bm=2):Lj.N?b.Uc=1:Fr()?b.Uc=2:b.Uc=3;var d={6:!1};Lj.H===2?d[7]=!0:Lj.H===1&&(d[2]=!0);if(uc){var e=Kk(Qk(uc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.fm=d;return df(b)};function Mr(a,b,c,d){var e,f=Number(a.Dc!=null?a.Dc:void 0);f!==0&&(e=new Date((b||zb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Fc:d}};var Nr=["ad_storage","ad_user_data"];function Or(a,b){if(!a)return db("TAGGING",32),10;if(b===null||b===void 0||b==="")return db("TAGGING",33),11;var c=Pr(!1);if(c.error!==0)return db("TAGGING",34),c.error;if(!c.value)return db("TAGGING",35),2;c.value[a]=b;var d=Qr(c);d!==0&&db("TAGGING",36);return d}
function Rr(a){if(!a)return db("TAGGING",27),{error:10};var b=Pr();if(b.error!==0)return db("TAGGING",29),b;if(!b.value)return db("TAGGING",30),{error:2};if(!(a in b.value))return db("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(db("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Pr(a){a=a===void 0?!0:a;if(!Vm(Nr))return db("TAGGING",43),{error:3};try{if(!x.localStorage)return db("TAGGING",44),{error:1}}catch(f){return db("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return db("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return db("TAGGING",47),{error:12}}}catch(f){return db("TAGGING",48),{error:8}}if(b.schema!=="gcl")return db("TAGGING",49),{error:4};
if(b.version!==1)return db("TAGGING",50),{error:5};try{var e=Sr(b);a&&e&&Qr({value:b,error:0})}catch(f){return db("TAGGING",48),{error:8}}return{value:b,error:0}}
function Sr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,db("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Sr(a[e.value])||c;return c}return!1}
function Qr(a){if(a.error)return a.error;if(!a.value)return db("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return db("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return db("TAGGING",53),7}return 0};var Tr={xj:"value",Hb:"conversionCount"},Ur=[Tr,{qm:10,Fm:11,xj:"timeouts",Hb:"timeouts"}];function Vr(){var a=Tr;if(!Wr(a))return{};var b=Xr(Ur),c=b[a.Hb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Hb]=c+1,d));return Yr(e)?e:b}
function Xr(a){var b;a:{var c=Rr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Wr(m)){var n=e[m.xj];n===void 0||Number.isNaN(n)?f[m.Hb]=-1:f[m.Hb]=Number(n)}else f[m.Hb]=-1}return f}
function Yr(a,b){b=b||{};for(var c=zb(),d=Mr(b,c,!0),e={},f=l(Ur),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Hb];m!==void 0&&m!==-1&&(e[h.xj]=m)}e.creationTimeMs=c;return Or("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Wr(a){return Vm(["ad_storage","ad_user_data"])?!a.Fm||Ja(a.Fm):!1}function Zr(a){return Vm(["ad_storage","ad_user_data"])?!a.qm||Ja(a.qm):!1};function $r(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var as={O:{Ao:0,Uj:1,wg:2,bk:3,Nh:4,Yj:5,Zj:6,dk:7,Oh:8,kl:9,jl:10,zi:11,ml:12,ah:13,pl:14,Mf:15,xo:16,ue:17,Si:18,Ti:19,Ui:20,Tl:21,Vi:22,Qh:23,kk:24}};as.O[as.O.Ao]="RESERVED_ZERO";as.O[as.O.Uj]="ADS_CONVERSION_HIT";as.O[as.O.wg]="CONTAINER_EXECUTE_START";as.O[as.O.bk]="CONTAINER_SETUP_END";as.O[as.O.Nh]="CONTAINER_SETUP_START";as.O[as.O.Yj]="CONTAINER_BLOCKING_END";as.O[as.O.Zj]="CONTAINER_EXECUTE_END";as.O[as.O.dk]="CONTAINER_YIELD_END";as.O[as.O.Oh]="CONTAINER_YIELD_START";as.O[as.O.kl]="EVENT_EXECUTE_END";
as.O[as.O.jl]="EVENT_EVALUATION_END";as.O[as.O.zi]="EVENT_EVALUATION_START";as.O[as.O.ml]="EVENT_SETUP_END";as.O[as.O.ah]="EVENT_SETUP_START";as.O[as.O.pl]="GA4_CONVERSION_HIT";as.O[as.O.Mf]="PAGE_LOAD";as.O[as.O.xo]="PAGEVIEW";as.O[as.O.ue]="SNIPPET_LOAD";as.O[as.O.Si]="TAG_CALLBACK_ERROR";as.O[as.O.Ti]="TAG_CALLBACK_FAILURE";as.O[as.O.Ui]="TAG_CALLBACK_SUCCESS";as.O[as.O.Tl]="TAG_EXECUTE_END";as.O[as.O.Vi]="TAG_EXECUTE_START";as.O[as.O.Qh]="CUSTOM_PERFORMANCE_START";as.O[as.O.kk]="CUSTOM_PERFORMANCE_END";var bs=[],cs={},ds={};var es=["1"];function fs(a){return a.origin!=="null"};function gs(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ja(12)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function hs(a,b,c,d){if(!is(d))return[];if(bs.includes("1")){var e;(e=Zc())==null||e.mark("1-"+as.O.Qh+"-"+(ds["1"]||0))}var f=gs(a,String(b||js()),c);if(bs.includes("1")){var g="1-"+as.O.kk+"-"+(ds["1"]||0),h={start:"1-"+as.O.Qh+"-"+(ds["1"]||0),end:g},m;(m=Zc())==null||m.mark(g);var n,p,q=(p=(n=Zc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&(ds["1"]=(ds["1"]||0)+1,cs["1"]=q+(cs["1"]||0))}return f}
function ks(a,b,c,d,e){if(is(e)){var f=ls(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=ms(f,function(g){return g.ep},b);if(f.length===1)return f[0];f=ms(f,function(g){return g.hq},c);return f[0]}}}function ns(a,b,c,d){var e=js(),f=window;fs(f)&&(f.document.cookie=a);var g=js();return e!==g||c!==void 0&&hs(b,g,!1,d).indexOf(c)>=0}
function os(a,b,c,d){function e(w,y,z){if(z==null)return delete h[y],w;h[y]=z;return w+"; "+y+"="+z}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!is(c.Fc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ps(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.bq);g=e(g,"samesite",c.uq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=qs(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!rs(u,c.path)&&ns(v,a,b,c.Fc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return rs(n,c.path)?1:ns(g,a,b,c.Fc)?0:1}function ss(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return os(a,b,c)}
function ms(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ls(a,b,c){for(var d=[],e=hs(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Vo:e[f],Wo:g.join("."),ep:Number(n[0])||1,hq:Number(n[1])||1})}}}return d}function ps(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ts=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,us=/(^|\.)doubleclick\.net$/i;function rs(a,b){return a!==void 0&&(us.test(window.document.location.hostname)||b==="/"&&ts.test(a))}function vs(a){if(!a)return 1;var b=a;Ja(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ws(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function xs(a,b){var c=""+vs(a),d=ws(b);d>1&&(c+="-"+d);return c}
var js=function(){return fs(window)?window.document.cookie:""},is=function(a){return a&&Ja(8)?(Array.isArray(a)?a:[a]).every(function(b){return Xm(b)&&Vm(b)}):!0},qs=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;us.test(e)||ts.test(e)||a.push("none");return a};function ys(a){var b=Math.round(Math.random()*2147483647);return a?String(b^$r(a)&2147483647):String(b)}function zs(a){return[ys(a),Math.round(zb()/1E3)].join(".")}function As(a,b,c,d,e){var f=vs(b),g;return(g=ks(a,f,ws(c),d,e))==null?void 0:g.Wo};var Bs;function Cs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ds,d=Es,e=Fs();if(!e.init){Ic(A,"mousedown",a);Ic(A,"keyup",a);Ic(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Gs(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Fs().decorators.push(f)}
function Hs(a,b,c){for(var d=Fs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Cb(e,g.callback())}}return e}
function Fs(){var a=vc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Is=/(.*?)\*(.*?)\*(.*)/,Js=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ks=/^(?:www\.|m\.|amp\.)+/,Ls=/([^?#]+)(\?[^#]*)?(#.*)?/;function Ms(a){var b=Ls.exec(a);if(b)return{Dj:b[1],query:b[2],fragment:b[3]}}function Ns(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Os(a,b){var c=[rc.userAgent,(new Date).getTimezoneOffset(),rc.userLanguage||rc.language,Math.floor(zb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Bs)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Bs=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Bs[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ps(a){return function(b){var c=Qk(x.location.href),d=c.search.replace("?",""),e=Hk(d,"_gl",!1,!0)||"";b.query=Qs(e)||{};var f=Kk(c,"fragment"),g;var h=-1;if(Eb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Qs(g||"")||{};a&&Rs(c,d,f)}}function Ss(a,b){var c=Ns(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Rs(a,b,c){function d(g,h){var m=Ss("_gl",g);m.length&&(m=h+m);return m}if(qc&&qc.replaceState){var e=Ns("_gl");if(e.test(b)||e.test(c)){var f=Kk(a,"path");b=d(b,"?");c=d(c,"#");qc.replaceState({},"",""+f+b+c)}}}function Ts(a,b){var c=Ps(!!b),d=Fs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Cb(e,f.query),a&&Cb(e,f.fragment));return e}
var Qs=function(a){try{var b=Us(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=bb(d[e+1]);c[f]=g}db("TAGGING",6);return c}}catch(h){db("TAGGING",8)}};function Us(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Is.exec(d);if(f){c=f;break a}d=Jk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Os(h,p)){m=!0;break a}m=!1}if(m)return h;db("TAGGING",7)}}}
function Vs(a,b,c,d,e){function f(p){p=Ss(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Ms(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Dj+h+m}
function Ws(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(ab(String(y))))}var z=v.join("*");u=["1",Os(z),z].join("*");d?(Ja(3)||Ja(1)||!p)&&Xs("_gl",u,a,p,q):Ys("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Hs(b,1,d),f=Hs(b,2,d),g=Hs(b,4,d),h=Hs(b,3,d);c(e,!1,!1);c(f,!0,!1);Ja(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Zs(m,h[m],a)}function Zs(a,b,c){c.tagName.toLowerCase()==="a"?Ys(a,b,c):c.tagName.toLowerCase()==="form"&&Xs(a,b,c)}function Ys(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ja(5)||d)){var h=x.location.href,m=Ms(c.href),n=Ms(h);g=!(m&&n&&m.Dj===n.Dj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Vs(a,b,c.href,d,e);fc.test(p)&&(c.href=p)}}
function Xs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Vs(a,b,f,d,e);fc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ds(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Ws(e,e.hostname)}}catch(g){}}function Es(a){try{var b=a.getAttribute("action");if(b){var c=Kk(Qk(b),"host");Ws(a,c)}}catch(d){}}function $s(a,b,c,d){Cs();var e=c==="fragment"?2:1;d=!!d;Gs(a,b,e,d,!1);e===2&&db("TAGGING",23);d&&db("TAGGING",24)}
function at(a,b){Cs();Gs(a,[Mk(x.location,"host",!0)],b,!0,!0)}function bt(){var a=A.location.hostname,b=Js.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Jk(f[2])||"":Jk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ks,""),m=e.replace(Ks,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function ct(a,b){return a===!1?!1:a||b||bt()};var dt=["1"],et={},ft={};function gt(a,b){b=b===void 0?!0:b;var c=ht(a.prefix);if(et[c])it(a);else if(jt(c,a.path,a.domain)){var d=ft[ht(a.prefix)]||{id:void 0,Fh:void 0};b&&kt(a,d.id,d.Fh);it(a)}else{var e=Sk("auiddc");if(e)db("TAGGING",17),et[c]=e;else if(b){var f=ht(a.prefix),g=zs();lt(f,g,a);jt(c,a.path,a.domain);it(a,!0)}}}
function it(a,b){if((b===void 0?0:b)&&Wr(Tr)){var c=Pr(!1);c.error!==0?db("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Qr(c)!==0&&db("TAGGING",41)):db("TAGGING",40):db("TAGGING",39)}if(Zr(Tr)&&Xr([Tr])[Tr.Hb]===-1){for(var d={},e=(d[Tr.Hb]=0,d),f=l(Ur),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Tr&&Zr(h)&&(e[h.Hb]=0)}Yr(e,a)}}
function kt(a,b,c){var d=ht(a.prefix),e=et[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(zb()/1E3)));lt(d,h,a,g*1E3)}}}}function lt(a,b,c,d){var e;e=["1",xs(c.domain,c.path),b].join(".");var f=Mr(c,d);f.Fc=mt();ss(a,e,f)}function jt(a,b,c){var d=As(a,b,c,dt,mt());if(!d)return!1;nt(a,d);return!0}
function nt(a,b){var c=b.split(".");c.length===5?(et[a]=c.slice(0,2).join("."),ft[a]={id:c.slice(2,4).join("."),Fh:Number(c[4])||0}):c.length===3?ft[a]={id:c.slice(0,2).join("."),Fh:Number(c[2])||0}:et[a]=b}function ht(a){return(a||"_gcl")+"_au"}function ot(a){function b(){Vm(c)&&a()}var c=mt();an(function(){b();Vm(c)||bn(b,c)},c)}
function pt(a){var b=Ts(!0),c=ht(a.prefix);ot(function(){var d=b[c];if(d){nt(c,d);var e=Number(et[c].split(".")[1])*1E3;if(e){db("TAGGING",16);var f=Mr(a,e);f.Fc=mt();var g=["1",xs(a.domain,a.path),d].join(".");ss(c,g,f)}}})}function qt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=As(a,e.path,e.domain,dt,mt());h&&(g[a]=h);return g};ot(function(){$s(f,b,c,d)})}function mt(){return["ad_storage","ad_user_data"]};function rt(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Qj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function st(a,b){var c=rt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Qj]||(d[c[e].Qj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Qj].push(g)}}return d};var tt={},ut=(tt.k={da:/^[\w-]+$/},tt.b={da:/^[\w-]+$/,Kj:!0},tt.i={da:/^[1-9]\d*$/},tt.h={da:/^\d+$/},tt.t={da:/^[1-9]\d*$/},tt.d={da:/^[A-Za-z0-9_-]+$/},tt.j={da:/^\d+$/},tt.u={da:/^[1-9]\d*$/},tt.l={da:/^[01]$/},tt.o={da:/^[1-9]\d*$/},tt.g={da:/^[01]$/},tt.s={da:/^.+$/},tt);var vt={},At=(vt[5]={Kh:{2:wt},wj:"2",uh:["k","i","b","u"]},vt[4]={Kh:{2:wt,GCL:xt},wj:"2",uh:["k","i","b"]},vt[2]={Kh:{GS2:wt,GS1:zt},wj:"GS2",uh:"sogtjlhd".split("")},vt);function Bt(a,b,c){var d=At[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kh[e];if(f)return f(a,b)}}}
function wt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=At[b];if(f){for(var g=f.uh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=ut[p];r&&(r.Kj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Ct(a,b,c){var d=At[b];if(d)return[d.wj,c||"1",Dt(a,b)].join(".")}
function Dt(a,b){var c=At[b];if(c){for(var d=[],e=l(c.uh),f=e.next();!f.done;f=e.next()){var g=f.value,h=ut[g];if(h){var m=a[g];if(m!==void 0)if(h.Kj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function xt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function zt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Et=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ft(a,b,c){if(At[b]){for(var d=[],e=hs(a,void 0,void 0,Et.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Bt(g.value,b,c);h&&d.push(Gt(h))}return d}}function Ht(a,b,c,d,e){d=d||{};var f=xs(d.domain,d.path),g=Ct(b,c,f);if(!g)return 1;var h=Mr(d,e,void 0,Et.get(c));return ss(a,g,h)}function It(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Gt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=ut[e];d.Uf?d.Uf.Kj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return It(h,g.Uf)}}(d)):void 0:typeof f==="string"&&It(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Jt=function(){this.value=0};Jt.prototype.set=function(a){return this.value|=1<<a};var Kt=function(a,b){b<=0||(a.value|=1<<b-1)};Jt.prototype.get=function(){return this.value};Jt.prototype.clear=function(a){this.value&=~(1<<a)};Jt.prototype.clearAll=function(){this.value=0};Jt.prototype.equals=function(a){return this.value===a.value};function Lt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Mt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Nt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Mb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Mb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a($r((""+b+e).toLowerCase()))};var Ot={},Pt=(Ot.gclid=!0,Ot.dclid=!0,Ot.gbraid=!0,Ot.wbraid=!0,Ot),Qt=/^\w+$/,Rt=/^[\w-]+$/,St={},Tt=(St.aw="_aw",St.dc="_dc",St.gf="_gf",St.gp="_gp",St.gs="_gs",St.ha="_ha",St.ag="_ag",St.gb="_gb",St),Ut=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Vt=/^www\.googleadservices\.com$/;function Wt(){return["ad_storage","ad_user_data"]}function Xt(a){return!Ja(8)||Vm(a)}function Yt(a,b){function c(){var d=Xt(b);d&&a();return d}an(function(){c()||bn(c,b)},b)}
function Zt(a){return $t(a).map(function(b){return b.gclid})}function au(a){return bu(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function bu(a){var b=cu(a.prefix),c=du("gb",b),d=du("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=$t(c).map(e("gb")),g=eu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function fu(a,b,c,d,e,f){var g=ob(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Gd=f),g.labels=gu(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Gd:f})}function eu(a){for(var b=Ft(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=hu(f);if(n){var p=void 0;Ja(9)&&(p=f.u);fu(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}
function $t(a){for(var b=[],c=hs(a,A.cookie,void 0,Wt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=iu(e.value);if(f!=null){var g=f;fu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ju(b)}function ku(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function lu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Jt,q=(n=b.Ka)!=null?n:new Jt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Gd=b.Gd);d.labels=ku(d.labels||[],b.labels||[]);d.Cb=ku(d.Cb||[],b.Cb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function mu(a){if(!a)return new Jt;var b=new Jt;if(a===1)return Kt(b,2),Kt(b,3),b;Kt(b,a);return b}
function nu(){var a=Rr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Rt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Jt;typeof e==="number"?g=mu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Cb:[2]}}catch(h){return null}}
function ou(){var a=Rr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Rt))return b;var f=new Jt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Cb:[2]});return b},[])}catch(b){return null}}
function pu(a){for(var b=[],c=hs(a,A.cookie,void 0,Wt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=iu(e.value);f!=null&&(f.Gd=void 0,f.Ka=new Jt,f.Cb=[1],lu(b,f))}var g=nu();g&&(g.Gd=void 0,g.Cb=g.Cb||[2],lu(b,g));if(Ja(14)){var h=ou();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Gd=void 0;p.Cb=p.Cb||[2];lu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ju(b)}
function gu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function cu(a){return a&&typeof a==="string"&&a.match(Qt)?a:"_gcl"}function qu(a,b){if(a){var c={value:a,Ka:new Jt};Kt(c.Ka,b);return c}}
function ru(a,b,c){var d=Qk(a),e=Kk(d,"query",!1,void 0,"gclsrc"),f=qu(Kk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=qu(Hk(g,"gclid",!1),3));e||(e=Hk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function su(a,b){var c=Qk(a),d=Kk(c,"query",!1,void 0,"gclid"),e=Kk(c,"query",!1,void 0,"gclsrc"),f=Kk(c,"query",!1,void 0,"wbraid");f=Kb(f);var g=Kk(c,"query",!1,void 0,"gbraid"),h=Kk(c,"query",!1,void 0,"gad_source"),m=Kk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Hk(n,"gclid",!1);e=e||Hk(n,"gclsrc",!1);f=f||Hk(n,"wbraid",!1);g=g||Hk(n,"gbraid",!1);h=h||Hk(n,"gad_source",!1)}return tu(d,e,m,f,g,h)}function uu(){return su(x.location.href,!0)}
function tu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Rt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Rt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Rt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Rt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function vu(a){for(var b=uu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=su(x.document.referrer,!1),b.gad_source=void 0);wu(b,!1,a)}
function xu(a){vu(a);var b=ru(x.location.href,!0,!1);b.length||(b=ru(x.document.referrer,!1,!0));a=a||{};yu(a);if(b.length){var c=b[0],d=zb(),e=Mr(a,d,!0),f=Wt(),g=function(){Xt(f)&&e.expires!==void 0&&Or("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};an(function(){g();Xt(f)||bn(g,f)},f)}}
function yu(a){var b;if(b=Ja(15)){var c=zu();b=Ut.test(c)||Vt.test(c)||Au()}if(b){var d;a:{for(var e=Qk(x.location.href),f=Ik(Kk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Pt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Lt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Mt(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,z=w.next().value,B=y,D=z,F=B&7;if(B>>3===16382){if(F!==0)break;var H=Mt(t,D);if(H===
void 0)break;r=l(H).next().value===1;break c}var J;d:{var S=void 0,ca=t,U=D;switch(F){case 0:J=(S=Mt(ca,U))==null?void 0:S[1];break d;case 1:J=U+8;break d;case 2:var na=Mt(ca,U);if(na===void 0)break;var T=l(na),Z=T.next().value;J=T.next().value+Z;break d;case 5:J=U+4;break d}J=void 0}if(J===void 0||J>t.length)break;u=J}}catch(V){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var X=d;X&&Bu(X,7,a)}}
function Bu(a,b,c){c=c||{};var d=zb(),e=Mr(c,d,!0),f=Wt(),g=function(){if(Xt(f)&&e.expires!==void 0){var h=ou()||[];lu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:mu(b)},!0);Or("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};an(function(){Xt(f)?g():bn(g,f)},f)}
function wu(a,b,c,d,e){c=c||{};e=e||[];var f=cu(c.prefix),g=d||zb(),h=Math.round(g/1E3),m=Wt(),n=!1,p=!1,q=function(){if(Xt(m)){var r=Mr(c,g,!0);r.Fc=m;for(var t=function(S,ca){var U=du(S,f);U&&(ss(U,ca,r),S!=="gb"&&(n=!0))},u=function(S){var ca=["GCL",h,S];e.length>0&&ca.push(e.join("."));return ca.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var z=a.gb[0],B=du("gb",f);!b&&$t(B).some(function(S){return S.gclid===z&&S.labels&&
S.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&Xt("ad_storage")&&(p=!0,!n)){var D=a.gbraid,F=du("ag",f);if(b||!eu(F).some(function(S){return S.gclid===D&&S.labels&&S.labels.length>0})){var H={},J=(H.k=D,H.i=""+h,H.b=e,H);Ht(F,J,5,c,g)}}Cu(a,f,g,c)};an(function(){q();Xt(m)||bn(q,m)},m)}
function Cu(a,b,c,d){if(a.gad_source!==void 0&&Xt("ad_storage")){if(Ja(4)){var e=Yc();if(e==="r"||e==="h")return}var f=a.gad_source,g=du("gs",b);if(g){var h=Math.floor((zb()-(Xc()||0))/1E3),m;if(Ja(9)){var n=Nt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Ht(g,m,5,d,c)}}}
function Du(a,b){var c=Ts(!0);Yt(function(){for(var d=cu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Tt[f]!==void 0){var g=du(f,d),h=c[g];if(h){var m=Math.min(Eu(h),zb()),n;b:{for(var p=m,q=hs(g,A.cookie,void 0,Wt()),r=0;r<q.length;++r)if(Eu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Mr(b,m,!0);t.Fc=Wt();ss(g,h,t)}}}}wu(tu(c.gclid,c.gclsrc),!1,b)},Wt())}
function Fu(a){var b=["ag"],c=Ts(!0),d=cu(a.prefix);Yt(function(){for(var e=0;e<b.length;++e){var f=du(b[e],d);if(f){var g=c[f];if(g){var h=Bt(g,5);if(h){var m=hu(h);m||(m=zb());var n;a:{for(var p=m,q=Ft(f,5),r=0;r<q.length;++r)if(hu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ht(f,h,5,a,m)}}}}},["ad_storage"])}function du(a,b){var c=Tt[a];if(c!==void 0)return b+c}function Eu(a){return Gu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function hu(a){return a?(Number(a.i)||0)*1E3:0}function iu(a){var b=Gu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Gu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Rt.test(a[2])?[]:a}
function Hu(a,b,c,d,e){if(Array.isArray(b)&&fs(x)){var f=cu(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=du(a[m],f);if(n){var p=hs(n,A.cookie,void 0,Wt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Yt(function(){$s(g,b,c,d)},Wt())}}
function Iu(a,b,c,d){if(Array.isArray(a)&&fs(x)){var e=["ag"],f=cu(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=du(e[m],f);if(!n)return{};var p=Ft(n,5);if(p.length){var q=p.sort(function(r,t){return hu(t)-hu(r)})[0];h[n]=Ct(q,5)}}return h};Yt(function(){$s(g,a,b,c)},["ad_storage"])}}function ju(a){return a.filter(function(b){return Rt.test(b.gclid)})}
function Ju(a,b){if(fs(x)){for(var c=cu(b.prefix),d={},e=0;e<a.length;e++)Tt[a[e]]&&(d[a[e]]=Tt[a[e]]);Yt(function(){sb(d,function(f,g){var h=hs(c+g,A.cookie,void 0,Wt());h.sort(function(t,u){return Eu(u)-Eu(t)});if(h.length){var m=h[0],n=Eu(m),p=Gu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Gu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];wu(q,!0,b,n,p)}})},Wt())}}
function Ku(a){var b=["ag"],c=["gbraid"];Yt(function(){for(var d=cu(a.prefix),e=0;e<b.length;++e){var f=du(b[e],d);if(!f)break;var g=Ft(f,5);if(g.length){var h=g.sort(function(q,r){return hu(r)-hu(q)})[0],m=hu(h),n=h.b,p={};p[c[e]]=h.k;wu(p,!0,a,m,n)}}},["ad_storage"])}function Lu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Mu(a){function b(h,m,n){n&&(h[m]=n)}if(Ym()){var c=uu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Ts(!1)._gs);if(Lu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);at(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);at(function(){return g},1)}}}function Au(){var a=Qk(x.location.href);return Kk(a,"query",!1,void 0,"gad_source")}
function Nu(a){if(!Ja(1))return null;var b=Ts(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ja(2)){b=Au();if(b!=null)return b;var c=uu();if(Lu(c,a))return"0"}return null}function Ou(a){var b=Nu(a);b!=null&&at(function(){var c={};return c.gad_source=b,c},4)}function Pu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Qu(a,b,c,d){var e=[];c=c||{};if(!Xt(Wt()))return e;var f=$t(a),g=Pu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Mr(c,p,!0);r.Fc=Wt();ss(a,q,r)}return e}
function Ru(a,b){var c=[];b=b||{};var d=bu(b),e=Pu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=cu(b.prefix),n=du(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Ht(n,y,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),B=Mr(b,u,!0);B.Fc=Wt();ss(n,z,B)}}return c}
function Su(a,b){var c=cu(b),d=du(a,c);if(!d)return 0;var e;e=a==="ag"?eu(d):$t(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Tu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Uu(a){var b=Math.max(Su("aw",a),Tu(Xt(Wt())?st():{})),c=Math.max(Su("gb",a),Tu(Xt(Wt())?st("_gac_gb",!0):{}));c=Math.max(c,Su("ag",a));return c>b}
function zu(){return A.referrer?Kk(Qk(A.referrer),"host"):""};function iv(){return tp("dedupe_gclid",function(){return zs()})};var jv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,kv=/^www.googleadservices.com$/;function lv(a){a||(a=mv());return a.Lq?!1:a.Jp||a.Kp||a.Np||a.Lp||a.Zf||a.up||a.Mp||a.zp?!0:!1}function mv(){var a={},b=Ts(!0);a.Lq=!!b._up;var c=uu();a.Jp=c.aw!==void 0;a.Kp=c.dc!==void 0;a.Np=c.wbraid!==void 0;a.Lp=c.gbraid!==void 0;a.Mp=c.gclsrc==="aw.ds";a.Zf=Xu().Zf;var d=A.referrer?Kk(Qk(A.referrer),"host"):"";a.zp=jv.test(d);a.up=kv.test(d);return a};function nv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function ov(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function pv(){return["ad_storage","ad_user_data"]}function qv(a){if(E(38)&&!rn(nn.Z.Cl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{nv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(qn(nn.Z.Cl,function(d){d.gclid&&Bu(d.gclid,5,a)}),ov(c)||M(178))})}catch(c){M(177)}};an(function(){Xt(pv())?b():bn(b,pv())},pv())}};var rv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function sv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?qn(nn.Z.Of,{gadSource:a.data.gadSource}):M(173)}
function tv(a,b){if(E(a)){if(rn(nn.Z.Of))return M(176),nn.Z.Of;if(rn(nn.Z.El))return M(170),nn.Z.Of;var c=Hl();if(!c)M(171);else if(c.opener){var d=function(g){if(rv.includes(g.origin)){a===119?sv(g):a===200&&(sv(g),g.data.gclid&&Bu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Qq(c,"message",d)}else M(172)};if(Pq(c,"message",d)){qn(nn.Z.El,!0);for(var e=l(rv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);M(174);return nn.Z.Of}M(175)}}}
;var uv=function(){this.C=this.gppString=void 0};uv.prototype.reset=function(){this.C=this.gppString=void 0};var vv=new uv;var wv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),xv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,yv=/^\d+\.fls\.doubleclick\.net$/,zv=/;gac=([^;?]+)/,Av=/;gacgb=([^;?]+)/;
function Bv(a,b){if(yv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(wv)?Jk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Cv(a,b,c){for(var d=Xt(Wt())?st("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Qu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{tp:f?e.join(";"):"",rp:Bv(d,Av)}}function Dv(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(xv)?b[1]:void 0}
function Ev(a){var b=Ja(9),c={},d,e,f;yv.test(A.location.host)&&(d=Dv("gclgs"),e=Dv("gclst"),b&&(f=Dv("gcllp")));if(d&&e&&(!b||f))c.yh=d,c.Ah=e,c.zh=f;else{var g=zb(),h=eu((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Gd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.yh=m.join("."),c.Ah=n.join("."),b&&p.length>0&&(c.zh=p.join(".")))}return c}
function Fv(a,b,c,d){d=d===void 0?!1:d;if(yv.test(A.location.host)){var e=Dv(c);if(e){if(d){var f=new Jt;Kt(f,2);Kt(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Cb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?pu(g):$t(g)}if(b==="wbraid")return $t((a||"_gcl")+"_gb");if(b==="braids")return bu({prefix:a})}return[]}function Gv(a){return yv.test(A.location.host)?!(Dv("gclaw")||Dv("gac")):Uu(a)}
function Hv(a,b,c){var d;d=c?Ru(a,b):Qu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Iv(){var a=x.__uspapi;if(jb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function Vv(a){var b=N(a.D,K.m.Nc),c=N(a.D,K.m.Mc);b&&!c?(a.eventName!==K.m.qa&&a.eventName!==K.m.Td&&M(131),a.isAborted=!0):!b&&c&&(M(132),a.isAborted=!0)}function Wv(a){var b=ip(K.m.U)?sp.pscdl:"denied";b!=null&&R(a,K.m.Jg,b)}function Xv(a){var b=Fl(!0);R(a,K.m.Lc,b)}function Yv(a){Er()&&R(a,K.m.be,1)}
function Mv(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Jk(a.substring(0,b))===void 0;)b--;return Jk(a.substring(0,b))||""}function Zv(a){$v(a,Ap.Ef.dn,N(a.D,K.m.pb))}function $v(a,b,c){Lv(a,K.m.ud)||R(a,K.m.ud,{});Lv(a,K.m.ud)[b]=c}function aw(a){Q(a,O.A.Qf,Mm.X.Da)}function bw(a){var b=gb("GTAG_EVENT_FEATURE_CHANNEL");b&&(R(a,K.m.kf,b),eb())}function cw(a){var b=a.D.getMergedValues(K.m.uc);b&&a.mergeHitDataForKey(K.m.uc,b)}
function dw(a,b){b=b===void 0?!1:b;if(E(108)){var c=P(a,O.A.Pf);if(c)if(c.indexOf(a.target.destinationId)<0){if(Q(a,O.A.Tj,!1),b||!ew(a,"custom_event_accept_rules",!1))a.isAborted=!0}else Q(a,O.A.Tj,!0)}}function fw(a){fl&&(Rn=!0,a.eventName===K.m.qa?Xn(a.D,a.target.id):(P(a,O.A.Le)||(Un[a.target.id]=!0),zp(P(a,O.A.ib))))};function pw(a,b,c,d){var e=Ec(),f;if(e===1)a:{var g=ck;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};
var uw=function(a,b){if(a)if(Fr()){}else if(lb(a)&&(a=Dp(a)),a){var c=void 0,d=!1,e=N(b,K.m.Wn);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=Dp(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=N(b,K.m.Xk),m;if(h){m=Array.isArray(h)?h:[h];var n=N(b,K.m.Vk),p=N(b,K.m.Wk),q=N(b,K.m.Yk),r=Do(N(b,K.m.Vn)),t=n||p,u=1;a.prefix!=="UA"||c||
(u=5);for(var v=0;v<m.length;v++)if(v<u)if(c)qw(c,m[v],r,b,{Ec:t,options:q});else if(a.prefix==="AW"&&a.ids[Fp[1]])E(155)?qw([a],m[v],r||"US",b,{Ec:t,options:q}):rw(a.ids[Fp[0]],a.ids[Fp[1]],m[v],b,{Ec:t,options:q});else if(a.prefix==="UA")if(E(155))qw([a],m[v],r||"US",b,{Ec:t});else{var w=a.destinationId,y=m[v],z={Ec:t};M(23);if(y){z=z||{};var B=sw(tw,z,w),D={};z.Ec!==void 0?D.receiver=z.Ec:D.replace=y;D.ga_wpid=w;D.destination=y;B(2,yb(),D)}}}}}},qw=function(a,b,c,d,e){M(21);if(b&&c){e=e||{};for(var f=
{countryNameCode:c,destinationNumber:b,retrievalTime:yb()},g=0;g<a.length;g++){var h=a[g];vw[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[Fp[0]],cl:h.ids[Fp[1]]},ww(f.adData,d),vw[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},vw[h.id]=!0))}(f.gaData||f.adData)&&sw(xw,e,void 0,d)(e.Ec,f,e.options)}},rw=function(a,b,c,d,e){M(22);if(c){e=e||{};var f=sw(yw,e,a,d),g={ak:a,cl:b};e.Ec===void 0&&(g.autoreplace=c);ww(g,d);f(2,e.Ec,g,c,0,yb(),e.options)}},
ww=function(a,b){a.dma=Br();Cr()&&(a.dmaCps=Ar());tr(b)?a.npa="0":a.npa="1"},sw=function(a,b,c,d){var e=x;if(e[a.functionName])return b.Cj&&Kc(b.Cj),e[a.functionName];var f=zw();e[a.functionName]=f;if(a.additionalQueues)for(var g=0;g<a.additionalQueues.length;g++)e[a.additionalQueues[g]]=e[a.additionalQueues[g]]||zw();a.idKey&&e[a.idKey]===void 0&&(e[a.idKey]=c);gm({destinationId:ig.ctid,endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},pw("https://","http://",a.scriptUrl),
b.Cj,b.fq);return f},zw=function(){function a(){a.q=a.q||[];a.q.push(arguments)}return a},yw={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},tw={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},Aw={Xm:"9",Co:"5"},xw={functionName:"_googCallTrackingImpl",additionalQueues:[tw.functionName,yw.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+
(Aw.Xm||Aw.Co)+".js"},vw={};function Bw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Lv(a,b)},setHitData:function(b,c){R(a,b,c)},setHitDataIfNotDefined:function(b,c){Lv(a,b)===void 0&&R(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return P(a,b)},setMetadata:function(b,c){Q(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return N(a.D,b)},Bb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return jd(c)?a.mergeHitDataForKey(b,c):!1}}};var Dw=function(a){var b=Cw[a.target.destinationId];if(!a.isAborted&&b)for(var c=Bw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Ew=function(a,b){var c=Cw[a];c||(c=Cw[a]=[]);c.push(b)},Cw={};function Gw(a,b){return arguments.length===1?Hw("set",a):Hw("set",a,b)}function Iw(a,b){return arguments.length===1?Hw("config",a):Hw("config",a,b)}function Jw(a,b,c){c=c||{};c[K.m.nd]=a;return Hw("event",b,c)}function Hw(){return arguments};var Lw=function(){this.messages=[];this.C=[]};Lw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Lw.prototype.listen=function(a){this.C.push(a)};
Lw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Lw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Mw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[O.A.ib]=ig.canonicalContainerId;Nw().enqueue(a,b,c)}
function Ow(){var a=Pw;Nw().listen(a)}function Nw(){return tp("mb",function(){return new Lw})};var Qw,Rw=!1;function Sw(){Rw=!0;Qw=Qw||{}}function Tw(a){Rw||Sw();return Qw[a]};function Uw(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Vw(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var ex=function(a){return a.tagName+":"+a.isVisible+":"+a.la.length+":"+dx.test(a.la)},sx=function(a){a=a||{Ce:!0,De:!0,Jh:void 0};a.Zb=a.Zb||{email:!0,phone:!1,address:!1};var b=fx(a),c=gx[b];if(c&&zb()-c.timestamp<200)return c.result;var d=hx(),e=d.status,f=[],g,h,m=[];if(!E(33)){if(a.Zb&&a.Zb.email){var n=ix(d.elements);f=jx(n,a&&a.Vf);g=kx(f);n.length>10&&(e="3")}!a.Jh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(lx(f[p],!!a.Ce,!!a.De));m=m.slice(0,10)}else if(a.Zb){}g&&(h=lx(g,!!a.Ce,!!a.De));var F={elements:m,
Gj:h,status:e};gx[b]={timestamp:zb(),result:F};return F},tx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},vx=function(a){var b=ux(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},ux=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},rx=function(a,b,c){var d=a.element,e={la:a.la,type:a.xa,tagName:d.tagName};b&&(e.querySelector=wx(d));c&&(e.isVisible=!Vw(d));return e},lx=function(a,b,c){return rx({element:a.element,la:a.la,xa:qx.kc},b,c)},fx=function(a){var b=!(a==null||!a.Ce)+"."+!(a==null||!a.De);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Zb&&(b+="."+a.Zb.email+"."+a.Zb.phone+"."+a.Zb.address);return b},kx=function(a){if(a.length!==0){var b;b=xx(a,function(c){return!yx.test(c.la)});b=xx(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=xx(b,function(c){return!Vw(c.element)});return b[0]}},jx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&si(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},xx=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},wx=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=wx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},ix=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(zx);if(f){var g=f[0],h;if(x.location){var m=Mk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,la:g})}}}return b},hx=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Ax.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Bx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||E(33)&&Cx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},zx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,dx=/@(gmail|googlemail)\./i,yx=/support|noreply/i,Ax="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Bx=
["BR"],Dx=tg('',2),qx={kc:"1",zd:"2",sd:"3",yd:"4",Ke:"5",Nf:"6",kh:"7",Ri:"8",Mh:"9",Mi:"10"},gx={},Cx=["INPUT","SELECT"],Ex=ux(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var hy=Number('')||5,iy=Number('')||50,jy=pb();
var ly=function(a,b){a&&(ky("sid",a.targetId,b),ky("cc",a.clientCount,b),ky("tl",a.totalLifeMs,b),ky("hc",a.heartbeatCount,b),ky("cl",a.clientLifeMs,b))},ky=function(a,b,c){b!=null&&c.push(a+"="+b)},my=function(){var a=A.referrer;if(a){var b;return Kk(Qk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},ny="https://"+Oi(21,"www.googletagmanager.com")+"/a?",py=function(){this.R=oy;this.N=0};py.prototype.H=function(a,b,c,d){var e=my(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&ky("si",a.hg,g);ky("m",0,g);ky("iss",f,g);ky("if",c,g);ly(b,g);d&&ky("fm",encodeURIComponent(d.substring(0,iy)),g);this.P(g);};py.prototype.C=function(a,b,c,d,e){var f=[];ky("m",1,f);ky("s",a,f);ky("po",my(),f);b&&(ky("st",b.state,f),ky("si",b.hg,f),ky("sm",b.qg,f));ly(c,f);ky("c",d,f);e&&ky("fm",encodeURIComponent(e.substring(0,
iy)),f);this.P(f);};py.prototype.P=function(a){a=a===void 0?[]:a;!el||this.N>=hy||(ky("pid",jy,a),ky("bc",++this.N,a),a.unshift("ctid="+ig.ctid+"&t=s"),this.R(""+ny+a.join("&")))};var qy=Number('')||500,ry=Number('')||5E3,sy=Number('20')||10,ty=Number('')||5E3;function uy(a){return a.performance&&a.performance.now()||Date.now()}
var vy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{xm:function(){},ym:function(){},wm:function(){},onFailure:function(){}}:h;this.Go=f;this.C=g;this.N=h;this.ba=this.ka=this.heartbeatCount=this.Eo=0;this.mh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.hg=uy(this.C);this.qg=uy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ba()};e.prototype.getState=function(){return{state:this.state,
hg:Math.round(uy(this.C)-this.hg),qg:Math.round(uy(this.C)-this.qg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.qg=uy(this.C))};e.prototype.Sl=function(){return String(this.Eo++)};e.prototype.Ba=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Sl(),maxDelay:this.nh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ba++,g.isDead||f.ba>sy){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.Do();var n,p;(p=(n=f.N).wm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Wl();else{if(f.heartbeatCount>g.stats.heartbeatCount+sy){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.mh){var u,v;(v=(u=f.N).ym)==null||v.call(u)}else{f.mh=!0;var w,y;(y=(w=f.N).xm)==null||y.call(w)}f.ba=0;f.Ho();f.Wl()}}})};e.prototype.nh=function(){return this.state===2?
ry:qy};e.prototype.Wl=function(){var f=this;this.C.setTimeout(function(){f.Ba()},Math.max(0,this.nh()-(uy(this.C)-this.ka)))};e.prototype.Ko=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Sl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Lf(t,7)},(p=f.maxDelay)!=null?p:ty),r={request:f,Km:g,Em:m,aq:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ka=uy(this.C);f.Em=!1;this.Go(f.request)};e.prototype.Ho=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.Em&&this.sendRequest(h)}};e.prototype.Do=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Lf(this.H[g.value],this.R)};e.prototype.Lf=function(f,g){this.rb(f);var h=f.request;h.failure={failureType:g};f.Km(h)};e.prototype.rb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.aq)};e.prototype.Hp=function(f){this.ka=uy(this.C);var g=this.H[f.requestId];if(g)this.rb(g),g.Km(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var wy;
var xy=function(){wy||(wy=new py);return wy},oy=function(a){kn(mn(Mm.X.Qc),function(){Hc(a)})},yy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},zy=function(a){var b=a,c=Lj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Ay=function(a){var b=rn(nn.Z.Ll);return b&&b[a]},By=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.Yo(a);x.setTimeout(function(){f.initialize()},1E3);Kc(function(){f.Rp(a,b,e)})};k=By.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),hg:this.initTime,qg:Math.round(zb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Ko(a,b,c)};k.getState=function(){return this.N.getState().state};k.Rp=function(a,b,c){var d=x.location.origin,e=this,
f=Fc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?yy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Fc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Hp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Yo=function(a){var b=this,c=vy(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{xm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},ym:function(){},wm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Cy(){var a=hg(eg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Dy(a,b){var c=Math.round(zb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Cy()||E(168))return;kk()&&(a=""+d+jk()+"/_/service_worker");var e=zy(a);if(e===null||Ay(e.origin))return;if(!sc()){xy().H(void 0,void 0,6);return}var f=new By(e,!!a,c||Math.round(zb()),xy(),b);sn(nn.Z.Ll)[e.origin]=f;}
var Ey=function(a,b,c,d){var e;if((e=Ay(a))==null||!e.delegate){var f=sc()?16:6;xy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Ay(a).delegate(b,c,d);};
function Fy(a,b,c,d,e){var f=zy();if(f===null){d(sc()?16:6);return}var g,h=(g=Ay(f.origin))==null?void 0:g.initTime,m=Math.round(zb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Ey(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Gy(a,b,c,d){var e=zy(a);if(e===null){d("_is_sw=f"+(sc()?16:6)+"te");return}var f=b?1:0,g=Math.round(zb()),h,m=(h=Ay(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);Ey(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Ay(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Hy(a){if(E(10)||kk()||Lj.N||Yk(a.D)||E(168))return;Dy(void 0,E(131));};var Iy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Jy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Ky(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Ly(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function My(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ny(a){if(!My(a))return null;var b=Jy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Iy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};function Ty(a){var b=a.location.href;if(a===a.top)return{url:b,Wp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Wp:c}};function Kz(a,b){var c=!!kk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?jk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(187)?Hz()?Iz():""+jk()+"/ag/g/c":Hz().toLowerCase()==="region1"?""+jk()+"/r1ag/g/c":""+jk()+"/ag/g/c":Iz();case 16:if(c){if(E(187))return Hz()?Jz():
""+jk()+"/ga/g/c";var d=Hz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+jk()+d}return Jz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?jk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?jk()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Lo+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?jk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(E(207)?c:c&&b.Eh)?jk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?jk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(E(207)?c:c&&b.Eh)?jk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";
case 55:return c?jk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":c?jk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(E(207)?c:c&&b.Eh)?jk()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:jc(a,"Unknown endpoint")}};function Lz(a){a=a===void 0?[]:a;return Mj(a).join("~")}function Mz(){if(!E(118))return"";var a,b;return(((a=Am(pm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Nz(a,b){b&&sb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var Vz={};Vz.O=as.O;var Wz={ir:"L",Bo:"S",zr:"Y",Nq:"B",Xq:"E",er:"I",wr:"TC",ar:"HTC"},Xz={Bo:"S",Wq:"V",Qq:"E",vr:"tag"},Yz={},Zz=(Yz[Vz.O.Ti]="6",Yz[Vz.O.Ui]="5",Yz[Vz.O.Si]="7",Yz);function $z(){function a(c,d){var e=gb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var aA=!1;function rA(a){}
function sA(a){}function tA(){}
function uA(a){}function vA(a){}
function wA(a){}
function xA(){}function yA(a,b){}
function zA(a,b,c){}
function AA(){};var BA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function CA(a,b,c,d,e,f,g){var h=Object.assign({},BA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});DA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():E(128)&&(b+="&_z=retryFetch",c?dm(a,b,c):cm(a,b))})};var EA=function(a){this.P=a;this.C=""},FA=function(a,b){a.H=b;return a},GA=function(a,b){a.N=b;return a},DA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}HA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},IA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};HA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},HA=function(a,b){b&&(JA(b.send_pixel,b.options,a.P),JA(b.create_iframe,b.options,a.H),JA(b.fetch,b.options,a.N))};function KA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function JA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=jd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var zB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),AB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},BB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},CB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function DB(){var a=qk("gtm.allowlist")||qk("gtm.whitelist");a&&M(9);Yj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);zB.test(x.location&&x.location.hostname)&&(Yj?M(116):(M(117),EB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Db(wb(a),AB),c=qk("gtm.blocklist")||qk("gtm.blacklist");c||(c=qk("tagTypeBlacklist"))&&M(3);c?M(8):c=[];zB.test(x.location&&x.location.hostname)&&(c=wb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
wb(c).indexOf("google")>=0&&M(2);var d=c&&Db(wb(c),BB),e={};return function(f){var g=f&&f[ef.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=gk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Yj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){M(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=qb(d,h||[]);t&&M(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Yj&&h.indexOf("cmpPartners")>=0?!FB():b&&b.indexOf("sandboxedScripts")!==-1?0:qb(d,CB))&&(u=!0);return e[g]=u}}function FB(){var a=hg(eg.C,ig.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var EB=!1;EB=!0;function GB(a,b,c,d,e){if(!HB()&&!Fm(a)){d.loadExperiments=Nj();om(a,d,e);var f=IB(a),g=function(){qm().container[a]&&(qm().container[a].state=3);JB()},h={destinationId:a,endpoint:0};if(kk())gm(h,jk()+"/"+f,void 0,g);else{var m=Eb(a,"GTM-"),n=Xk(),p=c?"/gtag/js":"/gtm.js",q=Wk(b,p+f);if(!q){var r=Pj.Ag+p;n&&uc&&m&&(r=uc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=pw("https://","http://",r+f)}gm(h,q,void 0,g)}}}
function JB(){Hm()||sb(Im(),function(a,b){KB(a,b.transportUrl,b.context);M(92)})}
function KB(a,b,c,d){if(!HB()&&!Gm(a))if(c.loadExperiments||(c.loadExperiments=Nj()),Hm()){var e;(e=qm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:pm()});qm().destination[a].state=0;rm({ctid:a,isDestination:!0},d);M(91)}else{var f;(f=qm().destination)[a]!=null||(f[a]={context:c,state:1,parent:pm()});qm().destination[a].state=1;rm({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(kk())gm(g,jk()+("/gtd"+IB(a,!0)));else{var h="/gtag/destination"+IB(a,!0),
m=Wk(b,h);m||(m=pw("https://","http://",Pj.Ag+h));gm(g,m)}}}function IB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Sj!=="dataLayer"&&(c+="&l="+Sj);if(!Eb(a,"GTM-")||b)c=E(130)?c+(kk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Lr();Xk()&&(c+="&sign="+Pj.Pi);var d=Lj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Nj().join("~")&&(c+="&tag_exp="+Nj().join("~"));return c}
function HB(){if(Fr()){return!0}return!1};var LB=function(){this.H=0;this.C={}};LB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,hc:c};return d};LB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var NB=function(a,b){var c=[];sb(MB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.hc===void 0||b.indexOf(e.hc)>=0)&&c.push(e.listener)});return c};function OB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:ig.ctid}};function PB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var RB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;QB(this,a,b)},SB=function(a,b,c,d){if(Uj.hasOwnProperty(b)||b==="__zone")return-1;var e={};jd(d)&&(e=kd(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},TB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},UB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},QB=function(a,b,c){b!==void 0&&a.Sf(b);c&&x.setTimeout(function(){UB(a)},
Number(c))};RB.prototype.Sf=function(a){var b=this,c=Bb(function(){Kc(function(){a(ig.ctid,b.eventData)})});this.C?c():this.P.push(c)};var VB=function(a){a.N++;return Bb(function(){a.H++;a.R&&a.H>=a.N&&UB(a)})},WB=function(a){a.R=!0;a.H>=a.N&&UB(a)};var XB={};function YB(){return x[ZB()]}var $B=function(a){if(Ym()){var b=YB();b(a+"require","linker");b(a+"linker:passthrough",!0)}},aC=function(a){var b=x;b.GoogleAnalyticsObject||(b.GoogleAnalyticsObject=a||"ga");var c=b.GoogleAnalyticsObject;if(b[c])b.hasOwnProperty(c);else{var d=function(){var e=xa.apply(0,arguments);d.q=d.q||[];d.q.push(e)};d.l=Number(yb());b[c]=d}return b[c]};
function ZB(){return x.GoogleAnalyticsObject||"ga"}function bC(){var a=ig.ctid;}
function cC(a,b){return function(){var c=YB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var iC=["es","1"],jC={},kC={};function lC(a,b){if(el){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";jC[a]=[["e",c],["eid",a]];vq(a)}}function mC(a){var b=a.eventId,c=a.Nd;if(!jC[b])return[];var d=[];kC[b]||d.push(iC);d.push.apply(d,ta(jC[b]));c&&(kC[b]=!0);return d};var nC={},oC={},pC={};function qC(a,b,c,d){el&&E(120)&&((d===void 0?0:d)?(pC[b]=pC[b]||0,++pC[b]):c!==void 0?(oC[a]=oC[a]||{},oC[a][b]=Math.round(c)):(nC[a]=nC[a]||{},nC[a][b]=(nC[a][b]||0)+1))}function rC(a){var b=a.eventId,c=a.Nd,d=nC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete nC[b];return e.length?[["md",e.join(".")]]:[]}
function sC(a){var b=a.eventId,c=a.Nd,d=oC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete oC[b];return e.length?[["mtd",e.join(".")]]:[]}function tC(){for(var a=[],b=l(Object.keys(pC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+pC[d])}return a.length?[["mec",a.join(".")]]:[]};var uC={},vC={};function wC(a,b,c){if(el&&b){var d=al(b);uC[a]=uC[a]||[];uC[a].push(c+d);var e=b[ef.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Hf[e]?"1":"2")+d;vC[a]=vC[a]||[];vC[a].push(f);vq(a)}}function xC(a){var b=a.eventId,c=a.Nd,d=[],e=uC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=vC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete uC[b],delete vC[b]);return d};function yC(a,b,c){c=c===void 0?!1:c;zC().addRestriction(0,a,b,c)}function AC(a,b,c){c=c===void 0?!1:c;zC().addRestriction(1,a,b,c)}function BC(){var a=xm();return zC().getRestrictions(1,a)}var CC=function(){this.container={};this.C={}},DC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
CC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=DC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
CC.prototype.getRestrictions=function(a,b){var c=DC(this,b);if(a===0){var d,e;return[].concat(ta((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ta((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ta((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ta((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
CC.prototype.getExternalRestrictions=function(a,b){var c=DC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};CC.prototype.removeExternalRestrictions=function(a){var b=DC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function zC(){return tp("r",function(){return new CC})};function EC(a,b,c,d){var e=Ff[a],f=FC(a,b,c,d);if(!f)return null;var g=Uf(e[ef.Ml],c,[]);if(g&&g.length){var h=g[0];f=EC(h.index,{onSuccess:f,onFailure:h.jm===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function FC(a,b,c,d){function e(){function w(){Yn(3);var J=zb()-H;wC(c.id,f,"7");TB(c.Rc,D,"exception",J);E(109)&&zA(c,f,Vz.O.Si);F||(F=!0,h())}if(f[ef.so])h();else{var y=Tf(f,c,[]),z=y[ef.Ym];if(z!=null)for(var B=0;B<z.length;B++)if(!ip(z[B])){h();return}var D=SB(c.Rc,String(f[ef.Ra]),Number(f[ef.qh]),y[ef.METADATA]),F=!1;y.vtp_gtmOnSuccess=function(){if(!F){F=!0;var J=zb()-H;wC(c.id,Ff[a],"5");TB(c.Rc,D,"success",J);E(109)&&zA(c,f,Vz.O.Ui);g()}};y.vtp_gtmOnFailure=function(){if(!F){F=!0;var J=zb()-
H;wC(c.id,Ff[a],"6");TB(c.Rc,D,"failure",J);E(109)&&zA(c,f,Vz.O.Ti);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);wC(c.id,f,"1");E(109)&&yA(c,f);var H=zb();try{Vf(y,{event:c,index:a,type:1})}catch(J){w(J)}E(109)&&zA(c,f,Vz.O.Tl)}}var f=Ff[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Uf(f[ef.Ul],c,[]);if(n&&n.length){var p=n[0],q=EC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.jm===
2?m:q}if(f[ef.Dl]||f[ef.vo]){var r=f[ef.Dl]?Gf:c.Fq,t=g,u=h;if(!r[a]){var v=GC(a,r,Bb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function GC(a,b,c){var d=[],e=[];b[a]=HC(d,e,c);return{onSuccess:function(){b[a]=IC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=JC;for(var f=0;f<e.length;f++)e[f]()}}}function HC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function IC(a){a()}function JC(a,b){b()};var MC=function(a,b){for(var c=[],d=0;d<Ff.length;d++)if(a[d]){var e=Ff[d];var f=VB(b.Rc);try{var g=EC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[ef.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Hf[h];c.push({Qm:d,priorityOverride:(m?m.priorityOverride||0:0)||PB(e[ef.Ra],1)||0,execute:g})}else KC(d,b),f()}catch(p){f()}}c.sort(LC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function NC(a,b){if(!MB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=NB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=VB(b);try{d[e](a,f)}catch(g){f()}}return!0}function LC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Qm,h=b.Qm;f=g>h?1:g<h?-1:0}return f}
function KC(a,b){if(el){var c=function(d){var e=b.isBlocked(Ff[d])?"3":"4",f=Uf(Ff[d][ef.Ml],b,[]);f&&f.length&&c(f[0].index);wC(b.id,Ff[d],e);var g=Uf(Ff[d][ef.Ul],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var OC=!1,MB;function PC(){MB||(MB=new LB);return MB}
function QC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(OC)return!1;OC=!0}var e=!1,f=BC(),g=kd(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}lC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:RC(g,e),Fq:[],logMacroError:function(){M(6);Yn(0)},cachedModelValues:SC(),Rc:new RB(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&el&&(n.reportMacroDiscrepancy=qC);E(109)&&vA(n.id);var p=$f(n);E(109)&&wA(n.id);e&&(p=TC(p));E(109)&&uA(b);var q=MC(p,n),r=NC(a,n.Rc);WB(n.Rc);d!=="gtm.js"&&d!=="gtm.sync"||bC();return UC(p,q)||r}function SC(){var a={};a.event=vk("event",1);a.ecommerce=vk("ecommerce",1);a.gtm=vk("gtm");a.eventModel=vk("eventModel");return a}
function RC(a,b){var c=DB();return function(d){if(c(d))return!0;var e=d&&d[ef.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=xm();f=zC().getRestrictions(0,g);var h=a;b&&(h=kd(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=gk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function TC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Ff[c][ef.Ra]);if(Tj[d]||Ff[c][ef.wo]!==void 0||PB(d,2))b[c]=!0}return b}function UC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Ff[c]&&!Uj[String(Ff[c][ef.Ra])])return!0;return!1};function VC(){PC().addListener("gtm.init",function(a,b){Lj.ba=!0;In();b()})};var WC=!1,XC=0,YC=[];function ZC(a){if(!WC){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){WC=!0;for(var e=0;e<YC.length;e++)Kc(YC[e])}YC.push=function(){for(var f=xa.apply(0,arguments),g=0;g<f.length;g++)Kc(f[g]);return 0}}}function $C(){if(!WC&&XC<140){XC++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");ZC()}catch(c){x.setTimeout($C,50)}}}
function aD(){var a=x;WC=!1;XC=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")ZC();else{Ic(A,"DOMContentLoaded",ZC);Ic(A,"readystatechange",ZC);if(A.createEventObject&&A.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&$C()}Ic(a,"load",ZC)}}function bD(a){WC?a():YC.push(a)};var cD={},dD={};function eD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Fj:void 0,mj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Fj=Dp(g,b),e.Fj){var h=wm();ob(h,function(r){return function(t){return r.Fj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=cD[g]||[];e.mj={};m.forEach(function(r){return function(t){r.mj[t]=!0}}(e));for(var n=ym(),p=0;p<n.length;p++)if(e.mj[n[p]]){c=c.concat(wm());break}var q=dD[g]||[];q.length&&(c=c.concat(q))}}return{zj:c,cq:d}}
function fD(a){sb(cD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function gD(a){sb(dD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var hD=!1,iD=!1;function jD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=kd(b,null),b[K.m.ef]&&(d.eventCallback=b[K.m.ef]),b[K.m.Pg]&&(d.eventTimeout=b[K.m.Pg]));return d}function kD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:wp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function lD(a,b){var c=a&&a[K.m.nd];c===void 0&&(c=qk(K.m.nd,2),c===void 0&&(c="default"));if(lb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?lb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=eD(d,b.isGtmEvent),f=e.zj,g=e.cq;if(g.length)for(var h=mD(a),m=0;m<g.length;m++){var n=Dp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=qm().destination[q];r&&r.state===0||KB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{zj:Ep(f,b.isGtmEvent),
Mo:Ep(t,b.isGtmEvent)}}}var nD=void 0,oD=void 0;function pD(a,b,c){var d=kd(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&M(136);var e=kd(b,null);kd(c,e);Mw(Iw(ym()[0],e),a.eventId,d)}function mD(a){for(var b=l([K.m.od,K.m.xc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Dq.C[d];if(e)return e}}
var qD={config:function(a,b){var c=kD(a,b);if(!(a.length<2)&&lb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!jd(a[2])||a.length>3)return;d=a[2]}var e=Dp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!um.qe){var m=Am(pm());if(Jm(m)){var n=m.parent,p=n.isDestination;h={gq:Am(n),Yp:p};break a}}h=void 0}var q=h;q&&(f=q.gq,g=q.Yp);lC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?wm().indexOf(r)===-1:ym().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Nc]){var u=mD(d);if(t)KB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;nD?pD(b,v,nD):oD||(oD=kd(v,null))}else GB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(M(128),g&&M(130),b.inheritParentConfig)){var w;var y=d;oD?(pD(b,oD,y),w=!1):(!y[K.m.rd]&&Wj&&nD||(nD=kd(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}fl&&(yp===1&&(An.mcc=!1),yp=2);if(Wj&&!t&&!d[K.m.rd]){var z=iD;iD=!0;if(z)return}hD||M(43);if(!b.noTargetGroup)if(t){gD(e.id);
var B=e.id,D=d[K.m.Sg]||"default";D=String(D).split(",");for(var F=0;F<D.length;F++){var H=dD[D[F]]||[];dD[D[F]]=H;H.indexOf(B)<0&&H.push(B)}}else{fD(e.id);var J=e.id,S=d[K.m.Sg]||"default";S=S.toString().split(",");for(var ca=0;ca<S.length;ca++){var U=cD[S[ca]]||[];cD[S[ca]]=U;U.indexOf(J)<0&&U.push(J)}}delete d[K.m.Sg];var na=b.eventMetadata||{};na.hasOwnProperty(O.A.wd)||(na[O.A.wd]=!b.fromContainerExecution);b.eventMetadata=na;delete d[K.m.ef];for(var T=t?[e.id]:wm(),Z=0;Z<T.length;Z++){var X=
d,V=T[Z],ka=kd(b,null),ia=Dp(V,ka.isGtmEvent);ia&&Dq.push("config",[X],ia,ka)}}}}},consent:function(a,b){if(a.length===3){M(39);var c=kD(a,b),d=a[1],e={},f=Bo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.vg?Array.isArray(h)?NaN:Number(h):g===K.m.jc?(Array.isArray(h)?h:[h]).map(Co):Do(h)}b.fromContainerExecution||(e[K.m.V]&&M(139),e[K.m.La]&&M(140));d==="default"?ep(e):d==="update"?gp(e,c):d==="declare"&&b.fromContainerExecution&&dp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&lb(c)){var d=void 0;if(a.length>2){if(!jd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=jD(c,d),f=kD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=lD(d,b);if(m){var n=m.zj,p=m.Mo,q,r,t;if(E(108)){q=p.map(function(J){return J.id});r=p.map(function(J){return J.destinationId});t=n.map(function(J){return J.id});for(var u=l(wm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<
0&&t.push(w)}}else q=n.map(function(J){return J.id}),r=n.map(function(J){return J.destinationId}),t=q;lC(g,c);for(var y=l(t),z=y.next();!z.done;z=y.next()){var B=z.value,D=kd(b,null),F=kd(d,null);delete F[K.m.ef];var H=D.eventMetadata||{};H.hasOwnProperty(O.A.wd)||(H[O.A.wd]=!D.fromContainerExecution);H[O.A.Ni]=q.slice();H[O.A.Pf]=r.slice();D.eventMetadata=H;Eq(c,F,B,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.nd]=q.join(","):delete e.eventModel[K.m.nd];hD||M(43);b.noGtmEvent===void 0&&
b.eventMetadata&&b.eventMetadata[O.A.Rl]&&(b.noGtmEvent=!0);e.eventModel[K.m.Mc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){M(53);if(a.length===4&&lb(a[1])&&lb(a[2])&&jb(a[3])){var c=Dp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){hD||M(43);var f=mD();if(ob(wm(),function(h){return c.destinationId===h})){kD(a,b);var g={};kd((g[K.m.sc]=d,g[K.m.Kc]=e,g),null);Fq(d,function(h){Kc(function(){e(h)})},c.id,b)}else KB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},
js:function(a,b){if(a.length===2&&a[1].getTime){hD=!0;var c=kD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&lb(a[1])&&jb(a[2])){if(fg(a[1],a[2]),M(74),a[1]==="all"){M(75);var b=!1;try{b=a[2](ig.ctid,"unknown",{})}catch(c){}b||M(76)}}else M(73)},set:function(a,b){var c=void 0;a.length===2&&jd(a[1])?c=kd(a[1],null):a.length===3&&lb(a[1])&&(c={},jd(a[2])||Array.isArray(a[2])?
c[a[1]]=kd(a[2],null):c[a[1]]=a[2]);if(c){var d=kD(a,b),e=d.eventId,f=d.priorityId;kd(c,null);var g=kd(c,null);Dq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},rD={policy:!0};var tD=function(a){if(sD(a))return a;this.value=a};tD.prototype.getUntrustedMessageValue=function(){return this.value};var sD=function(a){return!a||hd(a)!=="object"||jd(a)?!1:"getUntrustedMessageValue"in a};tD.prototype.getUntrustedMessageValue=tD.prototype.getUntrustedMessageValue;var uD=!1,vD=[];function wD(){if(!uD){uD=!0;for(var a=0;a<vD.length;a++)Kc(vD[a])}}function xD(a){uD?Kc(a):vD.push(a)};var yD=0,zD={},AD=[],BD=[],CD=!1,DD=!1;function ED(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function FD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return GD(a)}function HD(a,b){if(!mb(b)||b<0)b=0;var c=sp[Sj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function ID(a,b){var c=a._clear||b.overwriteModelFields;sb(a,function(e,f){e!=="_clear"&&(c&&tk(e),tk(e,f))});dk||(dk=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=wp(),a["gtm.uniqueEventId"]=d,tk("gtm.uniqueEventId",d));return QC(a)}function JD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(tb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function KD(){var a;if(BD.length)a=BD.shift();else if(AD.length)a=AD.shift();else return;var b;var c=a;if(CD||!JD(c.message))b=c;else{CD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=wp(),f=wp(),c.message["gtm.uniqueEventId"]=wp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};AD.unshift(n,c);b=h}return b}
function LD(){for(var a=!1,b;!DD&&(b=KD());){DD=!0;delete nk.eventModel;pk();var c=b,d=c.message,e=c.messageContext;if(d==null)DD=!1;else{e.fromContainerExecution&&uk();try{if(jb(d))try{d.call(rk)}catch(u){}else if(Array.isArray(d)){if(lb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=qk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(tb(d))a:{if(d.length&&lb(d[0])){var p=qD[d[0]];if(p&&(!e.fromContainerExecution||!rD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=ID(n,e)||a)}}finally{e.fromContainerExecution&&pk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=zD[String(q)]||[],t=0;t<r.length;t++)BD.push(MD(r[t]));r.length&&BD.sort(ED);delete zD[String(q)];q>yD&&(yD=q)}DD=!1}}}return!a}
function ND(){if(E(109)){var a=!Lj.ka;}var c=LD();if(E(109)){}try{var e=ig.ctid,f=x[Sj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Pw(a){if(yD<a.notBeforeEventId){var b=String(a.notBeforeEventId);zD[b]=zD[b]||[];zD[b].push(a)}else BD.push(MD(a)),BD.sort(ED),Kc(function(){DD||LD()})}function MD(a){return{message:a.message,messageContext:a.messageContext}}
function OD(){function a(f){var g={};if(sD(f)){var h=f;f=sD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=vc(Sj,[]),c=sp[Sj]=sp[Sj]||{};c.pruned===!0&&M(83);zD=Nw().get();Ow();bD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});xD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(sp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new tD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});AD.push.apply(AD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(M(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return LD()&&p};var e=b.slice(0).map(function(f){return a(f)});AD.push.apply(AD,e);if(!Lj.ka){if(E(109)){}Kc(ND)}}var GD=function(a){return x[Sj].push(a)};function PD(a){GD(a)};function QD(){var a,b=Qk(x.location.href);(a=b.hostname+b.pathname)&&En("dl",encodeURIComponent(a));var c;var d=ig.ctid;if(d){var e=um.qe?1:0,f,g=Am(pm());f=g&&g.context;c=d+";"+ig.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&En("tdp",h);var m=Fl(!0);m!==void 0&&En("frm",String(m))};function RD(){(Oo()||fl)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){M(179);var b=bm(a.effectiveDirective);if(b){var c;var d=$l(b,a.blockedURI);c=d?Yl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Im){p.Im=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Oo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Oo()){var u=Uo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;No(u)}}}Kn(p.endpoint)}}am(b,a.blockedURI)}}}}})};function SD(){var a;var b=zm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&En("pcid",e)};var TD=/^(https?:)?\/\//;
function UD(){var a=Bm();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=Zc())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(TD,"")===d.replace(TD,""))){b=g;break a}}M(146)}else M(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&En("rtg",String(a.canonicalContainerId)),En("slo",String(p)),En("hlo",a.htmlLoadOrder||"-1"),
En("lst",String(a.loadScriptType||"0")))}else M(144)};function VD(){var a=[],b=Number('1')||0,c=function(){var f=!1;return f}();a.push({Pm:195,Om:195,experimentId:104527906,controlId:104527907,percent:b,active:c,dj:1});var d=Number('1')||0,e=function(){var f=!1;
return f}();a.push({Pm:196,Om:196,experimentId:104528500,controlId:104528501,percent:d,active:e,dj:0});return a};var WD={};function XD(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Lj.R.H.add(Number(c.value))}function YD(){for(var a=l(VD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Pm;ji[d]=c;if(c.dj===1){var e=d,f=sn(nn.Z.yo);mi(f,e);XD(f)}else if(c.dj===0){var g=WD;mi(g,d);XD(g)}}};
function sE(){};var tE=function(){};tE.prototype.toString=function(){return"undefined"};var uE=new tE;function BE(a,b){function c(g){var h=Qk(g),m=Kk(h,"protocol"),n=Kk(h,"host",!0),p=Kk(h,"port"),q=Kk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function CE(a){return DE(a)?1:0}
function DE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=kd(a,{});kd({arg1:c[d],any_of:void 0},e);if(CE(e))return!0}return!1}switch(a["function"]){case "_cn":return Og(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Jg.length;g++){var h=Jg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Kg(b,c);case "_eq":return Pg(b,c);case "_ge":return Qg(b,c);case "_gt":return Sg(b,c);case "_lc":return Lg(b,c);case "_le":return Rg(b,
c);case "_lt":return Tg(b,c);case "_re":return Ng(b,c,a.ignore_case);case "_sw":return Ug(b,c);case "_um":return BE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var EE=function(a,b,c,d){Uq.call(this);this.mh=b;this.Lf=c;this.rb=d;this.Sa=new Map;this.nh=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};ra(EE,Uq);EE.prototype.N=function(){delete this.C;this.Sa.clear();this.ka.clear();this.Ba.clear();this.R&&(Qq(this.H,"message",this.R),delete this.R);delete this.H;delete this.rb;Uq.prototype.N.call(this)};
var FE=function(a){if(a.C)return a.C;a.Lf&&a.Lf(a.H)?a.C=a.H:a.C=El(a.H,a.mh);var b;return(b=a.C)!=null?b:null},HE=function(a,b,c){if(FE(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.yj){GE(a);var f=++a.nh;a.Ba.set(f,{Ih:e.Ih,bp:e.sm(c),persistent:b==="addEventListener"});a.C.postMessage(e.yj(c,f),"*")}}},GE=function(a){a.R||(a.R=function(b){try{var c;c=a.rb?a.rb(b):void 0;if(c){var d=c.jq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Ih)==null||f.call(e,
e.bp,c.payload)}}}catch(g){}},Pq(a.H,"message",a.R))};var IE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},JE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},KE={sm:function(a){return a.listener},yj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Ih:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},LE={sm:function(a){return a.listener},yj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Ih:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function ME(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,jq:b.__gppReturn.callId}}
var NE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Uq.call(this);this.caller=new EE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},ME);this.caller.Sa.set("addEventListener",IE);this.caller.ka.set("addEventListener",KE);this.caller.Sa.set("removeEventListener",JE);this.caller.ka.set("removeEventListener",LE);this.timeoutMs=c!=null?c:500};ra(NE,Uq);NE.prototype.N=function(){this.caller.dispose();Uq.prototype.N.call(this)};
NE.prototype.addEventListener=function(a){var b=this,c=hl(function(){a(OE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);HE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(PE,!0);return}a(QE,!0)}}})};
NE.prototype.removeEventListener=function(a){HE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var QE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},OE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},PE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function RE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){vv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");vv.C=d}}function SE(){try{var a=new NE(x,{timeoutMs:-1});FE(a.caller)&&a.addEventListener(RE)}catch(b){}};function TE(){var a=[["cv",Pi(1)],["rv",Qj],["tc",Ff.filter(function(b){return b}).length]];Rj&&a.push(["x",Rj]);ik()&&a.push(["tag_exp",ik()]);return a};var UE={};function Si(a){UE[a]=(UE[a]||0)+1}function VE(){for(var a=[],b=l(Object.keys(UE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+UE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var WE={},XE={};function YE(a){var b=a.eventId,c=a.Nd,d=[],e=WE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=XE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete WE[b],delete XE[b]);return d};function ZE(){return!1}function $E(){var a={};return function(b,c,d){}};function aF(){var a=bF;return function(b,c,d){var e=d&&d.event;cF(c);var f=zh(b)?void 0:1,g=new Ua;sb(c,function(r,t){var u=Ad(t,void 0,f);u===void 0&&t!==void 0&&M(44);g.set(r,u)});a.Ob(Yf());var h={am:mg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Rc.Sf(r)}:void 0,Kb:function(){return b},log:function(){},op:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},rq:!!PB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(ZE()){var m=$E(),n,p;h.xb={Pj:[],Tf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Gh:Rh()};h.log=function(r){var t=xa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=We(a,h,[b,g]);a.Ob();q instanceof Aa&&(q.type==="return"?q=q.data:q=void 0);return zd(q,void 0,f)}}function cF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;jb(b)&&(a.gtmOnSuccess=function(){Kc(b)});jb(c)&&(a.gtmOnFailure=function(){Kc(c)})};function dF(a){}dF.M="internal.addAdsClickIds";function eF(a,b){var c=this;}eF.publicName="addConsentListener";var fF=!1;function gF(a){for(var b=0;b<a.length;++b)if(fF)try{a[b]()}catch(c){M(77)}else a[b]()}function hF(a,b,c){var d=this,e;return e}hF.M="internal.addDataLayerEventListener";function iF(a,b,c){}iF.publicName="addDocumentEventListener";function jF(a,b,c,d){}jF.publicName="addElementEventListener";function kF(a){return a.K.tb()};function lF(a){}lF.publicName="addEventCallback";
function AF(a){}AF.M="internal.addFormAbandonmentListener";function BF(a,b,c,d){}
BF.M="internal.addFormData";var CF={},DF=[],EF={},FF=0,GF=0;
function NF(a,b){}NF.M="internal.addFormInteractionListener";
function UF(a,b){}UF.M="internal.addFormSubmitListener";
function ZF(a){}ZF.M="internal.addGaSendListener";function $F(a){if(!a)return{};var b=a.op;return OB(b.type,b.index,b.name)}function aG(a){return a?{originatingEntity:$F(a)}:{}};
var cG=function(a,b,c){bG().updateZone(a,b,c)},eG=function(a,b,c,d,e,f){var g=bG();c=c&&Db(c,dG);for(var h=g.createZone(a,c),m=0;m<b.length;m++){var n=String(b[m]);if(g.registerChild(n,ig.ctid,h)){var p=n,q=a,r=d,t=e,u=f;if(Eb(p,"GTM-"))GB(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Hw("js",yb());GB(p,void 0,!0,{source:1,fromContainerExecution:!0});var w={originatingEntity:t,inheritParentConfig:u};Mw(v,q,w);Mw(Iw(p,r),q,w)}}}return h},bG=function(){return tp("zones",function(){return new fG})},
gG={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},dG={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},fG=function(){this.C={};this.H={};this.N=0};k=fG.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.Ej],b))return!1;for(var e=0;e<c.tg.length;e++)if(this.H[c.tg[e]].Be(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.tg.length;f++){var g=this.H[c.tg[f]];g.Be(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.Ej],b);return function(m,n){n=n||[];if(!h(m,n))return!1;for(var p=0;p<e.length;++p)if(e[p].N(m,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.N);this.H[c]=new hG(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.P(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&sp[a]||!d&&Fm(a)||d&&d.Ej!==b)return!1;if(d)return d.tg.push(c),!1;this.C[a]={Ej:b,tg:[c]};return!0};var hG=function(a,b){this.H=null;this.C=[{eventId:a,Be:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};hG.prototype.P=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.Be!==b&&this.C.push({eventId:a,Be:b})};hG.prototype.Be=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].Be;return!1};hG.prototype.N=function(a,b){b=b||[];if(!this.H||gG[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function iG(a){var b=sp.zones;return b?b.getIsAllowedFn(ym(),a):function(){return!0}}function jG(){var a=sp.zones;a&&a.unregisterChild(ym())}
function kG(){AC(xm(),function(a){var b=sp.zones;return b?b.isActive(ym(),a.originalEventData["gtm.uniqueEventId"]):!0});yC(xm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return iG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var lG=function(a,b){this.tagId=a;this.xe=b};
function mG(a,b){var c=this;return a}mG.M="internal.loadGoogleTag";function nG(a){return new rd("",function(b){var c=this.evaluate(b);if(c instanceof rd)return new rd("",function(){var d=xa.apply(0,arguments),e=this,f=kd(kF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.sb();h.Ld(f);return c.Mb.apply(c,[h].concat(ta(g)))})})};function oG(a,b,c){var d=this;}oG.M="internal.addGoogleTagRestriction";var pG={},qG=[];
function xG(a,b){}
xG.M="internal.addHistoryChangeListener";function yG(a,b,c){}yG.publicName="addWindowEventListener";function zG(a,b){return!0}zG.publicName="aliasInWindow";function AG(a,b,c){}AG.M="internal.appendRemoteConfigParameter";function BG(a){var b;return b}
BG.publicName="callInWindow";function CG(a){}CG.publicName="callLater";function DG(a){}DG.M="callOnDomReady";function EG(a){}EG.M="callOnWindowLoad";function FG(a,b){var c;return c}FG.M="internal.computeGtmParameter";function GG(a,b){var c=this;}GG.M="internal.consentScheduleFirstTry";function HG(a,b){var c=this;}HG.M="internal.consentScheduleRetry";function IG(a){var b;return b}IG.M="internal.copyFromCrossContainerData";function JG(a,b){var c;var d=Ad(c,this.K,zh(kF(this).Kb())?2:1);d===void 0&&c!==void 0&&M(45);return d}JG.publicName="copyFromDataLayer";
function KG(a){var b=void 0;return b}KG.M="internal.copyFromDataLayerCache";function LG(a){var b;return b}LG.publicName="copyFromWindow";function MG(a){var b=void 0;return Ad(b,this.K,1)}MG.M="internal.copyKeyFromWindow";var NG=function(a){return a===Mm.X.Da&&dn[a]===Lm.Ia.pe&&!ip(K.m.U)};var OG=function(){return"0"},PG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Rk(a,b,"0")};var QG={},RG={},SG={},TG={},UG={},VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH=(oH[K.m.Qa]=(QG[2]=[NG],QG),oH[K.m.tf]=(RG[2]=[NG],RG),oH[K.m.ff]=(SG[2]=[NG],SG),oH[K.m.si]=(TG[2]=[NG],TG),oH[K.m.ui]=(UG[2]=[NG],UG),oH[K.m.wi]=(VG[2]=[NG],VG),oH[K.m.xi]=(WG[2]=[NG],WG),oH[K.m.yi]=(XG[2]=[NG],XG),oH[K.m.yc]=(YG[2]=[NG],YG),oH[K.m.vf]=(ZG[2]=[NG],ZG),oH[K.m.wf]=($G[2]=[NG],$G),oH[K.m.xf]=(aH[2]=[NG],aH),oH[K.m.yf]=(bH[2]=
[NG],bH),oH[K.m.zf]=(cH[2]=[NG],cH),oH[K.m.Af]=(dH[2]=[NG],dH),oH[K.m.Bf]=(eH[2]=[NG],eH),oH[K.m.Cf]=(fH[2]=[NG],fH),oH[K.m.mb]=(gH[1]=[NG],gH),oH[K.m.dd]=(hH[1]=[NG],hH),oH[K.m.hd]=(iH[1]=[NG],iH),oH[K.m.Xd]=(jH[1]=[NG],jH),oH[K.m.Qe]=(kH[1]=[function(a){return E(102)&&NG(a)}],kH),oH[K.m.jd]=(lH[1]=[NG],lH),oH[K.m.Aa]=(mH[1]=[NG],mH),oH[K.m.Wa]=(nH[1]=[NG],nH),oH),qH={},rH=(qH[K.m.mb]=OG,qH[K.m.dd]=OG,qH[K.m.hd]=OG,qH[K.m.Xd]=OG,qH[K.m.Qe]=OG,qH[K.m.jd]=function(a){if(!jd(a))return{};var b=kd(a,
null);delete b.match_id;return b},qH[K.m.Aa]=PG,qH[K.m.Wa]=PG,qH),sH={},tH={},uH=(tH[O.A.jb]=(sH[2]=[NG],sH),tH),vH={};var wH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};wH.prototype.getValue=function(a){a=a===void 0?Mm.X.Gb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};wH.prototype.H=function(){return hd(this.C)==="array"||jd(this.C)?kd(this.C,null):this.C};
var xH=function(){},yH=function(a,b){this.conditions=a;this.C=b},zH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new wH(c,e,g,a.C[b]||xH)},AH,BH;var CH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;Q(this,g,d[g])}},Lv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,P(a,O.A.Qf))},R=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(AH!=null||(AH=new yH(pH,rH)),e=zH(AH,b,c));d[b]=e};
CH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return R(this,a,b),!0;if(!jd(c))return!1;R(this,a,Object.assign(c,b));return!0};var DH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
CH.prototype.copyToHitData=function(a,b,c){var d=N(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&lb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&R(this,a,d)};
var P=function(a,b){var c=a.metadata[b];if(b===O.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,P(a,O.A.Qf))},Q=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(BH!=null||(BH=new yH(uH,vH)),e=zH(BH,b,c));d[b]=e},EH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},ew=function(a,b,c){var d=Tw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function FH(a,b){var c;return c}FH.M="internal.copyPreHit";function GH(a,b){var c=null;return Ad(c,this.K,2)}GH.publicName="createArgumentsQueue";function HH(a){return Ad(function(c){var d=YB();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
YB(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}HH.M="internal.createGaCommandQueue";function IH(a){return Ad(function(){if(!jb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
zh(kF(this).Kb())?2:1)}IH.publicName="createQueue";function JH(a,b){var c=null;return c}JH.M="internal.createRegex";function KH(a){}KH.M="internal.declareConsentState";function LH(a){var b="";return b}LH.M="internal.decodeUrlHtmlEntities";function MH(a,b,c){var d;return d}MH.M="internal.decorateUrlWithGaCookies";function NH(){}NH.M="internal.deferCustomEvents";function OH(a){var b;I(this,"detect_user_provided_data","auto");var c=zd(a)||{},d=sx({Ce:!!c.includeSelector,De:!!c.includeVisibility,Vf:c.excludeElementSelectors,Zb:c.fieldFilters,Jh:!!c.selectMultipleElements});b=new Ua;var e=new nd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(PH(f[g]));d.Gj!==void 0&&b.set("preferredEmailElement",PH(d.Gj));b.set("status",d.status);if(E(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(rc&&
rc.userAgent||"")){}return b}
var QH=function(a){switch(a){case qx.kc:return"email";case qx.zd:return"phone_number";case qx.sd:return"first_name";case qx.yd:return"last_name";case qx.Ri:return"street";case qx.Mh:return"city";case qx.Mi:return"region";case qx.Nf:return"postal_code";case qx.Ke:return"country"}},PH=function(a){var b=new Ua;b.set("userData",a.la);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(E(33)){}else switch(a.type){case qx.kc:b.set("type","email")}return b};OH.M="internal.detectUserProvidedData";
function TH(a,b){return f}TH.M="internal.enableAutoEventOnClick";
function aI(a,b){return p}aI.M="internal.enableAutoEventOnElementVisibility";function bI(){}bI.M="internal.enableAutoEventOnError";var cI={},dI=[],eI={},fI=0,gI=0;
function mI(a,b){var c=this;return d}mI.M="internal.enableAutoEventOnFormInteraction";
function rI(a,b){var c=this;return f}rI.M="internal.enableAutoEventOnFormSubmit";
function wI(){var a=this;}wI.M="internal.enableAutoEventOnGaSend";var xI={},yI=[];
function FI(a,b){var c=this;return f}FI.M="internal.enableAutoEventOnHistoryChange";var GI=["http://","https://","javascript:","file://"];
function KI(a,b){var c=this;return h}KI.M="internal.enableAutoEventOnLinkClick";var LI,MI;
function XI(a,b){var c=this;return d}XI.M="internal.enableAutoEventOnScroll";function YI(a){return function(){if(a.limit&&a.Bj>=a.limit)a.Dh&&x.clearInterval(a.Dh);else{a.Bj++;var b=zb();GD({event:a.eventName,"gtm.timerId":a.Dh,"gtm.timerEventNumber":a.Bj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Nm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Nm,"gtm.triggers":a.Kq})}}}
function ZI(a,b){
return f}ZI.M="internal.enableAutoEventOnTimer";var lc=va(["data-gtm-yt-inspected-"]),aJ=["www.youtube.com","www.youtube-nocookie.com"],bJ,cJ=!1;
function mJ(a,b){var c=this;return e}mJ.M="internal.enableAutoEventOnYouTubeActivity";cJ=!1;function nJ(a,b){if(!kh(a)||!eh(b))throw G(this.getName(),["string","Object|undefined"],arguments);var c=b?zd(b):{},d=a,e=!1;return e}nJ.M="internal.evaluateBooleanExpression";var oJ;function pJ(a){var b=!1;return b}pJ.M="internal.evaluateMatchingRules";function ZJ(){return nr(7)&&nr(9)&&nr(10)};
var cK=function(a,b){if(!b.isGtmEvent){var c=N(b,K.m.sc),d=N(b,K.m.Kc),e=N(b,c);if(e===void 0){var f=void 0;$J.hasOwnProperty(c)?f=$J[c]:aK.hasOwnProperty(c)&&(f=aK[c]);f===1&&(f=bK(c));lb(f)?YB()(function(){var g,h,m,n=(m=(g=YB())==null?void 0:(h=g.getByName)==null?void 0:h.call(g,a))==null?void 0:m.get(f);d(n)}):d(void 0)}else d(e)}},dK=function(a,b){var c=a[K.m.Oc],d=b+".",e=a[K.m.ma]||"",f=c===void 0?!!a.use_anchor:c==="fragment",g=!!a[K.m.vc];e=String(e).replace(/\s+/g,"").split(",");var h=YB();
h(d+"require","linker");h(d+"linker:autoLink",e,f,g)},hK=function(a,b,c){if(!c.isGtmEvent||!eK[a]){var d=!ip(K.m.ia),e=function(f){var g="gtm"+String(wp()),h,m=YB(),n=fK(b,"",c),p,q=n.createOnlyFields._useUp;if(c.isGtmEvent||gK(b,n.createOnlyFields)){c.isGtmEvent&&(h=n.createOnlyFields,n.gtmTrackerName&&(h.name=g));m(function(){var t,u=m==null?void 0:(t=m.getByName)==null?void 0:t.call(m,b);u&&(p=u.get("clientId"));if(!c.isGtmEvent){var v;m==null||(v=m.remove)==null||v.call(m,b)}});m("create",a,c.isGtmEvent?
h:n.createOnlyFields);d&&ip(K.m.ia)&&(d=!1,m(function(){var t,u,v=(t=YB())==null?void 0:(u=t.getByName)==null?void 0:u.call(t,c.isGtmEvent?g:b);!v||v.get("clientId")==p&&q||(c.isGtmEvent?(n.fieldsToSet["&gcu"]="1",n.fieldsToSet["&sst.gcut"]=yo[f]):(n.fieldsToSend["&gcu"]="1",n.fieldsToSend["&sst.gcut"]=yo[f]),v.set(n.fieldsToSet),
c.isGtmEvent?v.send("pageview"):v.send("pageview",n.fieldsToSend))}));c.isGtmEvent&&m(function(){var t;m==null||(t=m.remove)==null||t.call(m,g)})}};kp(function(){return void e(K.m.ia)},K.m.ia);kp(function(){return void e(K.m.U)},K.m.U);kp(function(){return void e(K.m.V)},K.m.V);c.isGtmEvent&&(eK[a]=!0)}},iK=function(a,b){Xk()&&b&&(a[K.m.rc]=b)},rK=function(a,b,c){function d(){var T=xa.apply(0,arguments);T[0]=w?w+"."+T[0]:""+T[0];u.apply(window,T)}function e(T){function Z(Sa,Ya){for(var Ga=0;Ya&&Ga<
Ya.length;Ga++)d(Sa,Ya[Ga])}var X=c.isGtmEvent,V=X?jK(y):kK(b,c);if(V){var ka={};iK(ka,T);d("require","ec","ec.js",ka);X&&V.bj&&d("set","&cu",V.bj);var ia=V.action;if(X||ia==="impressions")if(Z("ec:addImpression",V.om),!X)return;if(ia==="promo_click"||ia==="promo_view"||X&&V.og){var la=V.og;Z("ec:addPromo",la);if(la&&la.length>0&&ia==="promo_click"){X?d("ec:setAction",ia,V.Yb):d("ec:setAction",ia);return}if(!X)return}ia!=="promo_view"&&ia!=="impressions"&&(Z("ec:addProduct",V.Kd),d("ec:setAction",
ia,V.Yb))}}function f(T){if(T){var Z={};if(jd(T))for(var X in lK)lK.hasOwnProperty(X)&&mK(lK[X],X,T[X],Z);iK(Z,D);d("require","linkid",Z)}}function g(){if(Fr()){}else{var T=N(c,K.m.Un);T&&(d("require",T,{dataLayer:Sj}),d("require","render"))}}function h(){var T=N(c,K.m.bf);u(function(){if(!c.isGtmEvent&&jd(T)){var Z=y.fieldsToSend,X,V,ka=(X=v())==null?void 0:(V=X.getByName)==null?void 0:V.call(X,w),ia;for(ia in T)if(T[ia]!=
null&&/^(dimension|metric)\d+$/.test(ia)){var la=void 0,Sa=(la=ka)==null?void 0:la.get(bK(T[ia]));nK(Z,ia,Sa)}}})}function m(T,Z,X){X&&(Z=String(Z));y.fieldsToSend[T]=Z}function n(){if(y.displayfeatures){var T="_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","displayfeatures",void 0,{cookieName:T})}}var p=a;if(E(108)){var q=Dp(a),r=c.eventMetadata[O.A.Pf];if(q&&r&&r.indexOf(q.destinationId)<0)return}fl&&(Rn=!0,b===K.m.qa?Xn(c,a):(c.eventMetadata[O.A.Le]||(Un[a]=!0),zp(c.eventMetadata[O.A.ib])));
var t,u=c.isGtmEvent?aC(N(c,"gaFunctionName")):aC();if(jb(u)){var v=YB,w;w=c.isGtmEvent?N(c,"name")||N(c,"gtmTrackerName"):"gtag_"+p.split("-").join("_");var y=fK(w,b,c);!c.isGtmEvent&&gK(w,y.createOnlyFields)&&(u(function(){var T,Z;v()&&((T=v())==null||(Z=T.remove)==null||Z.call(T,w))}),oK[w]=!1);u("create",p,y.createOnlyFields);var z=c.isGtmEvent&&y.fieldsToSet[K.m.rc];if(!c.isGtmEvent&&y.createOnlyFields[K.m.rc]||z){var B=Wk(c.isGtmEvent?y.fieldsToSet[K.m.rc]:y.createOnlyFields[K.m.rc],"/analytics.js");
B&&(t=B)}var D=c.isGtmEvent?y.fieldsToSet[K.m.rc]:y.createOnlyFields[K.m.rc];if(D){var F=c.isGtmEvent?y.fieldsToSet[K.m.Qg]:y.createOnlyFields[K.m.Qg];F&&!oK[w]&&(oK[w]=!0,u(cC(w,F)))}c.isGtmEvent?y.enableRecaptcha&&d("require","recaptcha","recaptcha.js"):(h(),f(y.linkAttribution));var H=y[K.m.Pa];H&&H[K.m.ma]&&dK(H,w);d("set",y.fieldsToSet);if(c.isGtmEvent){if(y.enableLinkId){var J={};iK(J,D);d("require","linkid","linkid.js",J)}hK(p,w,c)}if(b===K.m.bd)if(c.isGtmEvent){n();if(y.remarketingLists){var S=
"_dc_gtm_"+p.replace(/[^A-Za-z0-9-]/g,"");d("require","adfeatures",{cookieName:S})}e(D);d("send","pageview");y.createOnlyFields._useUp&&$B(w+".")}else g(),d("send","pageview",y.fieldsToSend);else b===K.m.qa?(g(),uw(p,c),N(c,K.m.Fb)&&(Mu(["aw","dc"]),$B(w+".")),Ou(["aw","dc"]),y.sendPageView!=0&&d("send","pageview",y.fieldsToSend),hK(p,w,c)):b===K.m.Db?cK(w,c):b==="screen_view"?d("send","screenview",y.fieldsToSend):b==="timing_complete"?(y.fieldsToSend.hitType="timing",m("timingCategory",y.eventCategory,
!0),c.isGtmEvent?m("timingVar",y.timingVar,!0):m("timingVar",y.name,!0),m("timingValue",ub(y.value)),y.eventLabel!==void 0&&m("timingLabel",y.eventLabel,!0),d("send",y.fieldsToSend)):b==="exception"?d("send","exception",y.fieldsToSend):b===""&&c.isGtmEvent||(b==="track_social"&&c.isGtmEvent?(y.fieldsToSend.hitType="social",m("socialNetwork",y.socialNetwork,!0),m("socialAction",y.socialAction,!0),m("socialTarget",y.socialTarget,!0)):((c.isGtmEvent||pK[b])&&e(D),c.isGtmEvent&&n(),y.fieldsToSend.hitType=
"event",m("eventCategory",y.eventCategory,!0),m("eventAction",y.eventAction||b,!0),y.eventLabel!==void 0&&m("eventLabel",y.eventLabel,!0),y.value!==void 0&&m("eventValue",ub(y.value))),d("send",y.fieldsToSend));var ca=t&&!c.eventMetadata[O.A.Ql];if(!qK&&(!c.isGtmEvent||ca)){t=t||"https://www.google-analytics.com/analytics.js";qK=!0;var U=function(){c.onFailure()},na=function(){var T;((T=v())==null?0:T.loaded)||U()};Fr()?Kc(na):Dc(t,na,U)}}else Kc(c.onFailure)},sK=function(a,b,c,d){lp(function(){rK(a,
b,d)},[K.m.ia,K.m.U])},gK=function(a,b){var c=tK[a];tK[a]=kd(b,null);if(!c)return!1;for(var d in b)if(b.hasOwnProperty(d)&&b[d]!==c[d])return!0;for(var e in c)if(c.hasOwnProperty(e)&&c[e]!==b[e])return!0;return!1},kK=function(a,b){function c(u){return{id:d(K.m.Xa),affiliation:d(K.m.xk),revenue:d(K.m.Fa),tax:d(K.m.di),shipping:d(K.m.df),coupon:d(K.m.yk),list:d(K.m.bi)||d(K.m.cf)||u}}for(var d=function(u){return N(b,u)},e=d(K.m.sa),f,g=0;e&&g<e.length&&!(f=e[g][K.m.bi]||e[g][K.m.cf]);g++);var h=d(K.m.bf);
if(jd(h))for(var m=0;e&&m<e.length;++m){var n=e[m],p;for(p in h)h.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&h[p]!=null&&nK(n,p,n[h[p]])}var q=null,r=d(K.m.Mn);if(a===K.m.lb||a===K.m.Sd)q={action:a,Yb:c(),Kd:uK(e)};else if(a===K.m.Pd)q={action:"add",Yb:c(),Kd:uK(e)};else if(a===K.m.Qd)q={action:"remove",Yb:c(),Kd:uK(e)};else if(a===K.m.yb)q={action:"detail",Yb:c(f),Kd:uK(e)};else if(a===K.m.mc)q={action:"impressions",om:uK(e)};else if(a===K.m.nc)q={action:"promo_view",og:uK(r)||uK(e)};
else if(a==="select_content"&&r&&r.length>0||a===K.m.Ic)q={action:"promo_click",og:uK(r)||uK(e)};else if(a==="select_content"||a===K.m.Rd)q={action:"click",Yb:{list:d(K.m.bi)||d(K.m.cf)||f},Kd:uK(e)};else if(a===K.m.Zc||a==="checkout_progress"){var t={step:a===K.m.Zc?1:d(K.m.ai),option:d(K.m.Kg)};q={action:"checkout",Kd:uK(e),Yb:kd(c(),t)}}else a==="set_checkout_option"&&(q={action:"checkout_option",Yb:{step:d(K.m.ai),option:d(K.m.Kg)}});q&&(q.bj=d(K.m.Va));return q},jK=function(a){var b=a.gtmEcommerceData;
if(!b)return null;var c={};b.currencyCode&&(c.bj=b.currencyCode);if(b.impressions){c.action="impressions";var d=b.impressions;c.om=b.translateIfKeyEquals==="impressions"?uK(d):d}if(b.promoView){c.action="promo_view";var e=b.promoView.promotions;c.og=b.translateIfKeyEquals==="promoView"?uK(e):e}if(b.promoClick){var f=b.promoClick;c.action="promo_click";var g=f.promotions;c.og=b.translateIfKeyEquals==="promoClick"?uK(g):g;c.Yb=f.actionField;return c}for(var h in b)if(b[h]!==void 0&&h!=="translateIfKeyEquals"&&
h!=="impressions"&&h!=="promoView"&&h!=="promoClick"&&h!=="currencyCode"){c.action=h;var m=b[h].products;c.Kd=b.translateIfKeyEquals==="products"?uK(m):m;c.Yb=b[h].actionField;break}return Object.keys(c).length?c:null},uK=function(a){function b(e){function f(h,m){for(var n=0;n<m.length;n++){var p=m[n];if(e[p]){g[h]=e[p];break}}}var g=kd(e,null);f("id",["id","item_id","promotion_id"]);f("name",["name","item_name","promotion_name"]);f("brand",["brand","item_brand"]);f("variant",["variant","item_variant"]);
f("list",["list_name","item_list_name"]);f("position",["list_position","creative_slot","index"]);(function(){if(e.category)g.category=e.category;else{for(var h="",m=0;m<vK.length;m++)e[vK[m]]!==void 0&&(h&&(h+="/"),h+=e[vK[m]]);h&&(g.category=h)}})();f("listPosition",["list_position"]);f("creative",["creative_name"]);f("list",["list_name"]);f("position",["list_position","creative_slot"]);return g}for(var c=[],d=0;a&&d<a.length;d++)a[d]&&jd(a[d])&&c.push(b(a[d]));return c.length?c:void 0},fK=function(a,
b,c){var d=function(U){return N(c,U)},e={},f={},g={},h={},m=wK(d(K.m.Rn));!c.isGtmEvent&&m&&nK(f,"exp",m);g["&gtm"]=Kr({Ma:c.eventMetadata[O.A.ib],th:!0});c.isGtmEvent||(g._no_slc=!0);Ym()&&(h._cs=xK);var n=d(K.m.bf);if(!c.isGtmEvent&&jd(n))for(var p in n)if(n.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&n[p]!=null){var q=d(String(n[p]));q!==void 0&&nK(f,p,q)}for(var r=!c.isGtmEvent,t=Up(c),u=0;u<t.length;++u){var v=t[u];if(c.isGtmEvent){var w=d(v);yK.hasOwnProperty(v)?e[v]=w:zK.hasOwnProperty(v)?
h[v]=w:g[v]=w}else{var y=void 0;v!==K.m.oa?y=d(v):y=c.getMergedValues(v);if(AK.hasOwnProperty(v))mK(AK[v],v,y,e);else if(BK.hasOwnProperty(v))mK(BK[v],v,y,g);else if(aK.hasOwnProperty(v))mK(aK[v],v,y,f);else if($J.hasOwnProperty(v))mK($J[v],v,y,h);else if(/^(dimension|metric|content_group)\d+$/.test(v))mK(1,v,y,f);else if(v===K.m.oa){if(!CK){var z=Ib(y);z&&(f["&did"]=z)}var B=void 0,D=void 0;b===K.m.qa?B=Ib(c.getMergedValues(v),"."):(B=Ib(c.getMergedValues(v,1),"."),D=Ib(c.getMergedValues(v,2),"."));
B&&(f["&gdid"]=B);D&&(f["&edid"]=D)}else v===K.m.eb&&t.indexOf(K.m.gd)<0&&(h.cookieName=String(y)+"_ga");E(153)&&DK[v]&&(c.N.hasOwnProperty(v)||b===K.m.qa&&c.C.hasOwnProperty(v))&&(r=!1)}}E(153)&&r&&(f["&jsscut"]="1");d(K.m.Dg)!==!1&&d(K.m.Pb)!==!1&&ZJ()||(g.allowAdFeatures=!1);g.allowAdPersonalizationSignals=tr(c);!c.isGtmEvent&&d(K.m.Fb)&&(h._useUp=!0);if(c.isGtmEvent){h.name=h.name||e.gtmTrackerName;var F=g.hitCallback;g.hitCallback=function(){jb(F)&&F();c.onSuccess()}}else{nK(h,"cookieDomain",
"auto");nK(g,"forceSSL",!0);nK(e,"eventCategory",EK(b));FK[b]&&nK(f,"nonInteraction",!0);b==="login"||b==="sign_up"||b==="share"?nK(e,"eventLabel",d(K.m.Rk)):b==="search"||b==="view_search_results"?nK(e,"eventLabel",d(K.m.bo)):b==="select_content"&&nK(e,"eventLabel",d(K.m.Jn));var H=e[K.m.Pa]||{},J=H[K.m.de];J||J!=0&&H[K.m.ma]?h.allowLinker=!0:J===!1&&nK(h,"useAmpClientId",!1);f.hitCallback=c.onSuccess;h.name=a}ur()&&(g["&gcs"]=vr());g["&gcd"]=zr(c);Ym()&&(ip(K.m.ia)||(h.storage="none"),ip([K.m.U,
K.m.V])||(g.allowAdFeatures=!1,h.storeGac=!1));Cr()&&(g["&dma_cps"]=Ar());g["&dma"]=Br();Yq(fr())&&(g["&tcfd"]=Dr());ik()&&(g["&tag_exp"]=ik());var S=Yk(c)||d(K.m.rc),ca=d(K.m.Qg);S&&(c.isGtmEvent||(h[K.m.rc]=S),h._cd2l=!0);ca&&!c.isGtmEvent&&(h[K.m.Qg]=ca);e.fieldsToSend=f;e.fieldsToSet=g;e.createOnlyFields=h;return e},xK=function(a){return ip(a)},wK=function(a){if(Array.isArray(a)){for(var b=[],c=0;c<a.length;c++){var d=a[c];if(d!=null){var e=d.id,f=d.variant;e!=null&&f!=null&&b.push(String(e)+
"."+String(f))}}return b.length>0?b.join("!"):void 0}},nK=function(a,b,c){a.hasOwnProperty(b)||(a[b]=c)},EK=function(a){var b="general";GK[a]?b="ecommerce":HK[a]?b="engagement":a==="exception"&&(b="error");return b},bK=function(a){return a&&lb(a)?a.replace(/(_[a-z])/g,function(b){return b[1].toUpperCase()}):a},mK=function(a,b,c,d){if(c!==void 0)if(IK[b]&&(c=vb(c)),b!=="anonymize_ip"||c||(c=void 0),a===1)d[bK(b)]=c;else if(lb(a))d[a]=c;else for(var e in a)a.hasOwnProperty(e)&&c[e]!==void 0&&(d[a[e]]=
c[e])},CK=!1;var qK=!1,oK={},eK={},JK={},DK=(JK[K.m.Ea]=1,JK[K.m.Pb]=1,JK[K.m.ob]=1,JK[K.m.pb]=1,JK[K.m.zb]=1,JK[K.m.gd]=1,JK[K.m.Tb]=1,JK[K.m.eb]=1,JK[K.m.Jc]=1,JK[K.m.Tk]=1,JK[K.m.Aa]=1,JK[K.m.pf]=1,JK[K.m.Wa]=1,JK[K.m.Eb]=1,JK),KK={},$J=(KK.client_storage="storage",KK.sample_rate=1,KK.site_speed_sample_rate=1,KK.store_gac=1,KK.use_amp_client_id=1,KK[K.m.Rb]=1,KK[K.m.Oa]="storeGac",KK[K.m.ob]=
1,KK[K.m.pb]=1,KK[K.m.zb]=1,KK[K.m.gd]=1,KK[K.m.Tb]=1,KK[K.m.Jc]=1,KK),LK={},zK=(LK._cs=1,LK._useUp=1,LK.allowAnchor=1,LK.allowLinker=1,LK.alwaysSendReferrer=1,LK.clientId=1,LK.cookieDomain=1,LK.cookieExpires=1,LK.cookieFlags=1,LK.cookieName=1,LK.cookiePath=1,LK.cookieUpdate=1,LK.legacyCookieDomain=1,LK.legacyHistoryImport=1,LK.name=1,LK.sampleRate=1,LK.siteSpeedSampleRate=1,LK.storage=1,LK.storeGac=1,LK.useAmpClientId=1,LK._cd2l=1,LK),BK={anonymize_ip:1},MK={},aK=(MK.campaign={content:"campaignContent",
id:"campaignId",medium:"campaignMedium",name:"campaignName",source:"campaignSource",term:"campaignKeyword"},MK.app_id=1,MK.app_installer_id=1,MK.app_name=1,MK.app_version=1,MK.description="exDescription",MK.fatal="exFatal",MK.language=1,MK.page_hostname="hostname",MK.transport_type="transport",MK[K.m.Va]="currencyCode",MK[K.m.Vg]=1,MK[K.m.Aa]="location",MK[K.m.pf]="page",MK[K.m.Wa]="referrer",MK[K.m.Eb]="title",MK[K.m.li]=1,MK[K.m.Qa]=1,MK),NK={},AK=(NK.content_id=1,NK.event_action=1,NK.event_category=
1,NK.event_label=1,NK.link_attribution=1,NK.name=1,NK[K.m.Pa]=1,NK[K.m.Rk]=1,NK[K.m.qb]=1,NK[K.m.Fa]=1,NK),yK={displayfeatures:1,enableLinkId:1,enableRecaptcha:1,eventAction:1,eventCategory:1,eventLabel:1,gaFunctionName:1,gtmEcommerceData:1,gtmTrackerName:1,linker:1,remarketingLists:1,socialAction:1,socialNetwork:1,socialTarget:1,timingVar:1,value:1},vK=["item_category","item_category2","item_category3","item_category4","item_category5"],OK={},lK=(OK.levels=1,OK[K.m.pb]="duration",OK[K.m.gd]=1,OK),
PK={},IK=(PK.anonymize_ip=1,PK.fatal=1,PK.send_page_view=1,PK.store_gac=1,PK.use_amp_client_id=1,PK[K.m.Oa]=1,PK[K.m.Vg]=1,PK),QK={},pK=(QK.checkout_progress=1,QK.select_content=1,QK.set_checkout_option=1,QK[K.m.Pd]=1,QK[K.m.Qd]=1,QK[K.m.Zc]=1,QK[K.m.Rd]=1,QK[K.m.mc]=1,QK[K.m.Ic]=1,QK[K.m.nc]=1,QK[K.m.lb]=1,QK[K.m.Sd]=1,QK[K.m.yb]=1,QK),RK={},GK=(RK.checkout_progress=1,RK.set_checkout_option=1,RK[K.m.mk]=1,RK[K.m.nk]=1,RK[K.m.Pd]=1,RK[K.m.Qd]=1,RK[K.m.pk]=1,RK[K.m.Zc]=1,RK[K.m.lb]=1,RK[K.m.Sd]=1,
RK[K.m.qk]=1,RK),SK={},HK=(SK.generate_lead=1,SK.login=1,SK.search=1,SK.select_content=1,SK.share=1,SK.sign_up=1,SK.view_search_results=1,SK[K.m.Rd]=1,SK[K.m.mc]=1,SK[K.m.Ic]=1,SK[K.m.nc]=1,SK[K.m.yb]=1,SK),TK={},FK=(TK.view_search_results=1,TK[K.m.mc]=1,TK[K.m.nc]=1,TK[K.m.yb]=1,TK),tK={};function UK(a,b,c,d){}UK.M="internal.executeEventProcessor";function VK(a){var b;return Ad(b,this.K,1)}VK.M="internal.executeJavascriptString";function WK(a){var b;return b};function XK(a){var b="";return b}XK.M="internal.generateClientId";function YK(a){var b={};return Ad(b)}YK.M="internal.getAdsCookieWritingOptions";function ZK(a,b){var c=!1;return c}ZK.M="internal.getAllowAdPersonalization";function $K(){var a;return a}$K.M="internal.getAndResetEventUsage";function aL(a,b){b=b===void 0?!0:b;var c;return c}aL.M="internal.getAuid";var bL=null;
function cL(){var a=new Ua;return a}
cL.publicName="getContainerVersion";function dL(a,b){b=b===void 0?!0:b;var c;return c}dL.publicName="getCookieValues";function eL(){var a="";return a}eL.M="internal.getCorePlatformServicesParam";function fL(){return go()}fL.M="internal.getCountryCode";function gL(){var a=[];a=wm();return Ad(a)}gL.M="internal.getDestinationIds";function hL(a){var b=new Ua;return b}hL.M="internal.getDeveloperIds";function iL(a){var b;return b}iL.M="internal.getEcsidCookieValue";function jL(a,b){var c=null;return c}jL.M="internal.getElementAttribute";function kL(a){var b=null;return b}kL.M="internal.getElementById";function lL(a){var b="";return b}lL.M="internal.getElementInnerText";function mL(a,b){var c=null;return Ad(c)}mL.M="internal.getElementProperty";function nL(a){var b;return b}nL.M="internal.getElementValue";function oL(a){var b=0;return b}oL.M="internal.getElementVisibilityRatio";function pL(a){var b=null;return b}pL.M="internal.getElementsByCssSelector";
function qL(a){var b;if(!kh(a))throw G(this.getName(),["string"],arguments);I(this,"read_event_data",a);var c;a:{var d=a,e=kF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",z=l(n),B=z.next();!B.done;B=
z.next()){var D=B.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var F=l(w),H=F.next();!H.done;H=F.next()){if(f==null){c=void 0;break a}f=f[H.value]}c=f}else c=void 0}b=Ad(c,this.K,1);return b}qL.M="internal.getEventData";var rL={};rL.enableDecodeUri=E(92);rL.enableGaAdsConversions=E(122);rL.enableGaAdsConversionsClientId=E(121);rL.enableOverrideAdsCps=E(170);rL.enableUrlDecodeEventUsage=E(139);function sL(){return Ad(rL)}sL.M="internal.getFlags";function tL(){var a;return a}tL.M="internal.getGsaExperimentId";function uL(){return new wd(uE)}uL.M="internal.getHtmlId";function vL(a){var b;return b}vL.M="internal.getIframingState";function wL(a,b){var c={};return Ad(c)}wL.M="internal.getLinkerValueFromLocation";function xL(){var a=new Ua;return a}xL.M="internal.getPrivacyStrings";function yL(a,b){var c;if(!kh(a)||!kh(b))throw G(this.getName(),["string","string"],arguments);var d=Tw(a)||{};c=Ad(d[b],this.K);return c}yL.M="internal.getProductSettingsParameter";function zL(a,b){var c;return c}zL.publicName="getQueryParameters";function AL(a,b){var c;return c}AL.publicName="getReferrerQueryParameters";function BL(a){var b="";return b}BL.publicName="getReferrerUrl";function CL(){return ho()}CL.M="internal.getRegionCode";function DL(a,b){var c;return c}DL.M="internal.getRemoteConfigParameter";function EL(){var a=new Ua;a.set("width",0);a.set("height",0);return a}EL.M="internal.getScreenDimensions";function FL(){var a="";return a}FL.M="internal.getTopSameDomainUrl";function GL(){var a="";return a}GL.M="internal.getTopWindowUrl";function HL(a){var b="";return b}HL.publicName="getUrl";function IL(){I(this,"get_user_agent");return rc.userAgent}IL.M="internal.getUserAgent";function JL(){var a;return a?Ad(Oy(a)):a}JL.M="internal.getUserAgentClientHints";function RL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function SL(){var a=RL();a.hid=a.hid||pb();return a.hid}function TL(a,b){var c=RL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function qM(a){(fy(a)||kk())&&R(a,K.m.fl,ho()||go());!fy(a)&&kk()&&R(a,K.m.tl,"::")}function rM(a){if(kk()&&!fy(a)){var b=E(176);E(187)&&E(201)&&(b=b&&!ko());b&&R(a,K.m.Qk,!0);if(E(78)){Zv(a);$v(a,Ap.Ef.gn,Eo(N(a.D,K.m.eb)));var c=Ap.Ef.hn;var d=N(a.D,K.m.Jc);$v(a,c,d===!0?1:d===!1?0:void 0);$v(a,Ap.Ef.fn,Eo(N(a.D,K.m.zb)));$v(a,Ap.Ef.bn,xs(Do(N(a.D,K.m.ob)),Do(N(a.D,K.m.Tb))))}}};var NM={AW:nn.Z.Wm,G:nn.Z.io,DC:nn.Z.fo};function OM(a){var b=$i(a);return""+$r(b.map(function(c){return c.value}).join("!"))}function PM(a){var b=Dp(a);return b&&NM[b.prefix]}function QM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var uN=window,vN=document,wN=function(a){var b=uN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||vN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&uN["ga-disable-"+a]===!0)return!0;try{var c=uN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(vN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return vN.getElementById("__gaOptOutExtension")?!0:!1};
function IN(a){sb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Xb]||{};sb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function qO(a,b){}function rO(a,b){var c=function(){};return c}
function sO(a,b,c){};var tO=rO;var uO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function vO(a,b,c){var d=this;}vO.M="internal.gtagConfig";
function xO(a,b){}
xO.publicName="gtagSet";function yO(){var a={};return a};function zO(a){}zO.M="internal.initializeServiceWorker";function AO(a,b){}AO.publicName="injectHiddenIframe";var BO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function CO(a,b,c,d,e){}CO.M="internal.injectHtml";var GO={};
function IO(a,b,c,d){}var JO={dl:1,id:1},KO={};
function LO(a,b,c,d){}E(160)?LO.publicName="injectScript":IO.publicName="injectScript";LO.M="internal.injectScript";function MO(){return lo()}MO.M="internal.isAutoPiiEligible";function NO(a){var b=!0;return b}NO.publicName="isConsentGranted";function OO(a){var b=!1;return b}OO.M="internal.isDebugMode";function PO(){return jo()}PO.M="internal.isDmaRegion";function QO(a){var b=!1;return b}QO.M="internal.isEntityInfrastructure";function RO(a){var b=!1;if(!ph(a))throw G(this.getName(),["number"],[a]);b=E(a);return b}RO.M="internal.isFeatureEnabled";function SO(){var a=!1;return a}SO.M="internal.isFpfe";function TO(){var a=!1;return a}TO.M="internal.isGcpConversion";function UO(){var a=!1;return a}UO.M="internal.isLandingPage";function VO(){var a=!1;return a}VO.M="internal.isOgt";function WO(){var a;return a}WO.M="internal.isSafariPcmEligibleBrowser";function XO(){var a=Mh(function(b){kF(this).log("error",b)});a.publicName="JSON";return a};function YO(a){var b=void 0;return Ad(b)}YO.M="internal.legacyParseUrl";function ZO(){return!1}
var $O={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function aP(){}aP.publicName="logToConsole";function bP(a,b){}bP.M="internal.mergeRemoteConfig";function cP(a,b,c){c=c===void 0?!0:c;var d=[];return Ad(d)}cP.M="internal.parseCookieValuesFromString";function dP(a){var b=void 0;return b}dP.publicName="parseUrl";function eP(a){}eP.M="internal.processAsNewEvent";function fP(a,b,c){var d;return d}fP.M="internal.pushToDataLayer";function gP(a){var b=xa.apply(1,arguments),c=!1;if(!kh(a))throw G(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(zd(f.value,this.K,1));try{I.apply(null,d),c=!0}catch(g){return!1}return c}gP.publicName="queryPermission";function hP(a){var b=this;}hP.M="internal.queueAdsTransmission";function iP(a,b){var c=void 0;return c}iP.publicName="readAnalyticsStorage";function jP(){var a="";return a}jP.publicName="readCharacterSet";function kP(){return Sj}kP.M="internal.readDataLayerName";function lP(){var a="";return a}lP.publicName="readTitle";function mP(a,b){var c=this;if(!kh(a)||!gh(b))throw G(this.getName(),["string","function"],arguments);Ew(a,function(d){b.invoke(c.K,Ad(d,c.K,1))});}mP.M="internal.registerCcdCallback";function nP(a,b){return!0}nP.M="internal.registerDestination";var oP=["config","event","get","set"];function pP(a,b,c){}pP.M="internal.registerGtagCommandListener";function qP(a,b){var c=!1;return c}qP.M="internal.removeDataLayerEventListener";function rP(a,b){}
rP.M="internal.removeFormData";function sP(){}sP.publicName="resetDataLayer";function tP(a,b,c){var d=void 0;return d}tP.M="internal.scrubUrlParams";function uP(a){}uP.M="internal.sendAdsHit";function vP(a,b,c,d){}vP.M="internal.sendGtagEvent";function wP(a,b,c){}wP.publicName="sendPixel";function xP(a,b){}xP.M="internal.setAnchorHref";function yP(a){}yP.M="internal.setContainerConsentDefaults";function zP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}zP.publicName="setCookie";function AP(a){}AP.M="internal.setCorePlatformServices";function BP(a,b){}BP.M="internal.setDataLayerValue";function CP(a){}CP.publicName="setDefaultConsentState";function DP(a,b){}DP.M="internal.setDelegatedConsentType";function EP(a,b){}EP.M="internal.setFormAction";function FP(a,b,c){c=c===void 0?!1:c;}FP.M="internal.setInCrossContainerData";function GP(a,b,c){return!1}GP.publicName="setInWindow";function HP(a,b,c){}HP.M="internal.setProductSettingsParameter";function IP(a,b,c){if(!kh(a)||!kh(b)||arguments.length!==3)throw G(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Gq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!jd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=zd(c,this.K,1);}IP.M="internal.setRemoteConfigParameter";function JP(a,b){}JP.M="internal.setTransmissionMode";function KP(a,b,c,d){var e=this;}KP.publicName="sha256";function LP(a,b,c){}
LP.M="internal.sortRemoteConfigParameters";function MP(a){}MP.M="internal.storeAdsBraidLabels";function NP(a,b){var c=void 0;return c}NP.M="internal.subscribeToCrossContainerData";var OP={},PP={};OP.getItem=function(a){var b=null;return b};OP.setItem=function(a,b){};
OP.removeItem=function(a){};OP.clear=function(){};OP.publicName="templateStorage";function QP(a,b){var c=!1;return c}QP.M="internal.testRegex";function RP(a){var b;return b};function SP(a,b){var c;return c}SP.M="internal.unsubscribeFromCrossContainerData";function TP(a){}TP.publicName="updateConsentState";function UP(a){var b=!1;return b}UP.M="internal.userDataNeedsEncryption";var VP;function WP(a,b,c){VP=VP||new Xh;VP.add(a,b,c)}function XP(a,b){var c=VP=VP||new Xh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=jb(b)?sh(a,b):th(a,b)}
function YP(){return function(a){var b;var c=VP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.tb();if(e){var f=!1,g=e.Kb();if(g){zh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function ZP(){var a=function(c){return void XP(c.M,c)},b=function(c){return void WP(c.publicName,c)};b(eF);b(lF);b(zG);b(BG);b(CG);b(JG);b(LG);b(GH);b(XO());b(IH);b(cL);b(dL);b(zL);b(AL);b(BL);b(HL);b(xO);b(AO);b(NO);b(aP);b(dP);b(gP);b(jP);b(lP);b(wP);b(zP);b(CP);b(GP);b(KP);b(OP);b(TP);WP("Math",xh());WP("Object",Vh);WP("TestHelper",Zh());WP("assertApi",uh);WP("assertThat",vh);WP("decodeUri",Ah);WP("decodeUriComponent",Bh);WP("encodeUri",Ch);WP("encodeUriComponent",Dh);WP("fail",Ih);WP("generateRandom",
Jh);WP("getTimestamp",Kh);WP("getTimestampMillis",Kh);WP("getType",Lh);WP("makeInteger",Nh);WP("makeNumber",Oh);WP("makeString",Ph);WP("makeTableMap",Qh);WP("mock",Th);WP("mockObject",Uh);WP("fromBase64",WK,!("atob"in x));WP("localStorage",$O,!ZO());WP("toBase64",RP,!("btoa"in x));a(dF);a(hF);a(BF);a(NF);a(UF);a(ZF);a(oG);a(xG);a(AG);a(DG);a(EG);a(FG);a(GG);a(HG);a(IG);a(KG);a(MG);a(FH);a(HH);a(JH);a(KH);a(LH);a(MH);a(NH);a(OH);a(TH);a(aI);a(bI);a(mI);a(rI);a(wI);a(FI);a(KI);a(XI);a(ZI);a(mJ);a(nJ);
a(pJ);a(UK);a(VK);a(XK);a(YK);a(ZK);a($K);a(aL);a(fL);a(gL);a(hL);a(iL);a(jL);a(kL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(sL);a(tL);a(uL);a(vL);a(wL);a(xL);a(yL);a(CL);a(DL);a(EL);a(FL);a(GL);a(JL);a(vO);a(zO);a(CO);a(LO);a(MO);a(OO);a(PO);a(QO);a(RO);a(SO);a(TO);a(UO);a(VO);a(WO);a(YO);a(mG);a(bP);a(cP);a(eP);a(fP);a(hP);a(kP);a(mP);a(nP);a(pP);a(qP);a(rP);a(tP);a(uP);a(vP);a(xP);a(yP);a(AP);a(BP);a(DP);a(EP);a(FP);a(HP);a(IP);a(JP);a(LP);a(MP);a(NP);a(QP);a(SP);a(UP);XP("internal.IframingStateSchema",
yO());
E(104)&&a(eL);E(160)?b(LO):b(IO);E(177)&&b(iP);return YP()};var bF;
function $P(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;bF=new Ue;aQ();Bf=aF();var e=bF,f=ZP(),g=new sd("require",f);g.Ua();e.C.C.set("require",g);Pa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Xf(n,d[m]);try{bF.execute(n),E(120)&&el&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Pf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");gk[q]=["sandboxedScripts"]}bQ(b)}function aQ(){bF.Xc(function(a,b,c){sp.SANDBOXED_JS_SEMAPHORE=sp.SANDBOXED_JS_SEMAPHORE||0;sp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{sp.SANDBOXED_JS_SEMAPHORE--}})}function bQ(a){a&&sb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");gk[e]=gk[e]||[];gk[e].push(b)}})};function cQ(a){Mw(Gw("developer_id."+a,!0),0,{})};var dQ=Array.isArray;function eQ(a,b){return kd(a,b||null)}function W(a){return window.encodeURIComponent(a)}function fQ(a,b,c){Hc(a,b,c)}
function gQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Kk(Qk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function hQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function iQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=hQ(b,"parameter","parameterValue");e&&(c=eQ(e,c))}return c}var jQ=x.clearTimeout,kQ=x.setTimeout;function lQ(a,b,c){if(Fr()){b&&Kc(b)}else return Dc(a,b,c,void 0)}function mQ(){return x.location.href}function nQ(a,b){return qk(a,b||2)}function oQ(a,b){x[a]=b}function pQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}function qQ(a,b){if(Fr()){b&&Kc(b)}else Fc(a,b)}
var rQ={};var Y={securityGroups:{}};
Y.securityGroups.v=["google"],Y.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=nQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Y.__v.F="v",Y.__v.isVendorTemplate=!0,Y.__v.priorityOverride=0,Y.__v.isInfrastructure=!0,Y.__v["5"]=!0;
Y.securityGroups.rep=["google"],Y.__rep=function(a){var b=Dp(a.vtp_containerId,!0);if(b){var c,d;switch(b.prefix){case "AW":c=tJ;d=Mm.X.Da;break;case "DC":c=KJ;d=Mm.X.Da;break;case "GF":c=PJ;d=Mm.X.Gb;break;case "HA":c=VJ;d=Mm.X.Gb;break;case "UA":c=sK;d=Mm.X.Gb;break;case "MC":c=tO(b,a.vtp_gtmEventId);d=Mm.X.Hc;break;default:Kc(a.vtp_gtmOnFailure);return}c?(Kc(a.vtp_gtmOnSuccess),E(185)?Cq(a.vtp_containerId,c,d,a.vtp_remoteConfig):(Cq(a.vtp_containerId,c,d),a.vtp_remoteConfig&&Iq(a.vtp_containerId,
a.vtp_remoteConfig||{}))):Kc(a.vtp_gtmOnFailure)}else Kc(a.vtp_gtmOnFailure)},Y.__rep.F="rep",Y.__rep.isVendorTemplate=!0,Y.__rep.priorityOverride=0,Y.__rep.isInfrastructure=!1,Y.__rep["5"]=!1;
Y.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Y.__read_event_data=b;Y.__read_event_data.F="read_event_data";Y.__read_event_data.isVendorTemplate=!0;Y.__read_event_data.priorityOverride=0;Y.__read_event_data.isInfrastructure=!1;Y.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!lb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Ig(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();







Y.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Y.__detect_user_provided_data=b;Y.__detect_user_provided_data.F="detect_user_provided_data";Y.__detect_user_provided_data.isVendorTemplate=!0;Y.__detect_user_provided_data.priorityOverride=0;Y.__detect_user_provided_data.isInfrastructure=!1;Y.__detect_user_provided_data["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&e!==
"code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},T:a}})}();










Y.securityGroups.get=["google"],Y.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Jw(String(b.streamId),d,c);Mw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Y.__get.F="get",Y.__get.isVendorTemplate=!0,Y.__get.priorityOverride=0,Y.__get.isInfrastructure=!1,Y.__get["5"]=!1;


Y.securityGroups.zone=[],function(){var a={},b=function(d){for(var e=0;e<d.length;e++)if(!d[e])return!1;return!0},c=function(d){var e=b(d.vtp_boundaries||[]);if(d.vtp_gtmTagId in a)cG(a[d.vtp_gtmTagId],d.vtp_gtmEventId,e);else if(e){var f=d.vtp_childContainers.map(function(n){return n.publicId}),g=d.vtp_enableTypeRestrictions?d.vtp_whitelistedTypes.map(function(n){return n.typeId}):null,h={};var m=eG(d.vtp_gtmEventId,f,g,h,OB(1,d.vtp_gtmEntityIndex,d.vtp_gtmEntityName),!!d.vtp_inheritParentConfig);a[d.vtp_gtmTagId]=m}Kc(d.vtp_gtmOnSuccess)};Y.__zone=c;Y.__zone.F="zone";Y.__zone.isVendorTemplate=!0;Y.__zone.priorityOverride=0;Y.__zone.isInfrastructure=
!1;Y.__zone["5"]=!0}();

var vp={dataLayer:rk,callback:function(a){fk.hasOwnProperty(a)&&jb(fk[a])&&fk[a]();delete fk[a]},bootstrap:0};
function sQ(){up();Dm();JB();Cb(gk,Y.securityGroups);var a=Am(pm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;To(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||M(142);Of={Uo:cg}}var tQ=!1;
function co(){try{if(tQ||!Km()){Oj();Lj.P=Oi(18,"");
Lj.rb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Lj.Sa="ad_storage|analytics_storage|ad_user_data";Lj.Ba="56n0";Lj.Ba="56n0";if(E(109)){}Ha[8]=!0;var a=tp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});$o(a);rp();SE();gr();xp();if(Em()){jG();zC().removeExternalRestrictions(xm());}else{Mf();Hf=Y;Jf=CE;eg=new lg;$P();sQ();Gr();ao||($n=fo());
op();OD();aD();uD=!1;A.readyState==="complete"?wD():Ic(x,"load",wD);VC();el&&(kq(yq),x.setInterval(xq,864E5),kq(TE),kq(mC),kq($z),kq(Bq),kq(YE),kq(xC),E(120)&&(kq(rC),kq(sC),kq(tC)),UE={},kq(VE),Ri());fl&&(On(),Rp(),QD(),UD(),SD(),En("bt",String(Lj.C?2:Lj.N?1:0)),En("ct",String(Lj.C?0:Lj.N?1:Fr()?2:3)),RD());sE();Yn(1);kG();YD();ek=zb();vp.bootstrap=ek;Lj.ka&&ND();E(109)&&tA();E(134)&&(typeof x.name==="string"&&Eb(x.name,"web-pixel-sandbox-CUSTOM")&&$c()?cQ("dMDg0Yz"):x.Shopify&&(cQ("dN2ZkMj"),$c()&&cQ("dNTU0Yz")))}}}catch(b){Yn(4),uq()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Go(n)&&(m=h.ol)}function c(){m&&uc?g(m):a()}if(!x[Oi(37,"__TAGGY_INSTALLED")]){var d=!1;if(A.referrer){var e=Qk(A.referrer);d=Mk(e,"host")===Oi(38,"cct.google")}if(!d){var f=hs(Oi(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Oi(37,"__TAGGY_INSTALLED")]=!0,Dc(Oi(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";Yj&&(v="OGT",w="GTAG");
var y=Oi(23,"google.tagmanager.debugui2.queue"),z=x[y];z||(z=[],x[y]=z,Dc("https://"+Pj.Ag+"/debug/bootstrap?id="+ig.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Kr()));var B={messageType:"CONTAINER_STARTING",data:{scriptSource:uc,containerProduct:v,debug:!1,id:ig.ctid,targetRef:{ctid:ig.ctid,isDestination:vm()},aliases:ym(),destinations:wm()}};B.data.resume=function(){a()};Pj.Zm&&(B.data.initialPublish=!0);z.push(B)},h={lo:1,rl:2,Hl:3,jk:4,ol:5};h[h.lo]="GTM_DEBUG_LEGACY_PARAM";h[h.rl]="GTM_DEBUG_PARAM";h[h.Hl]="REFERRER";
h[h.jk]="COOKIE";h[h.ol]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Kk(x.location,"query",!1,void 0,"gtm_debug");Go(p)&&(m=h.rl);if(!m&&A.referrer){var q=Qk(A.referrer);Mk(q,"host")===Oi(24,"tagassistant.google.com")&&(m=h.Hl)}if(!m){var r=hs("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.jk)}m||b();if(!m&&Fo(n)){var t=!1;Ic(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&tQ&&!fo()["0"]?bo():co()});

})()

