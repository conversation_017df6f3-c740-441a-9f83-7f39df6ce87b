<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VCF Architect Exam Practice</title>
    <link rel="stylesheet" href="styles.css?v=2.2">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <h1>VCF Architect 2V0-13.24 Exam Practice</h1>
                    <div class="theme-selector">
                        <label for="theme-select">🎨</label>
                        <select id="theme-select">
                            <option value="gradient">Gradient</option>
                            <option value="dark">Dark</option>
                            <option value="light">Light</option>
                        </select>
                    </div>
                </div>
                <button id="home-btn" class="btn btn-home hidden">🏠 Home</button>
            </div>
            <div class="exam-info">
                <span id="exam-mode-display">Practice Mode</span>
                <span id="progress-display">Question 1 of 60</span>
                <span id="score-display">Score: 0/0</span>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Start Screen -->
            <div id="start-screen" class="screen active">
                <div class="start-container">
                    <h2>Welcome to VCF Architect Exam Practice</h2>
                    <p>Choose your exam mode and start practicing:</p>
                    <div class="alert alert-info" style="margin: 15px 0; padding: 10px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px;">
                        <strong>📝 Content Status:</strong> Questions 1-7 verified and corrected. Question 48 includes network diagram. All functionality working perfectly!
                    </div>
                    
                    <div class="mode-selection">
                        <div class="mode-card" data-mode="practice">
                            <h3>Practice Mode</h3>
                            <p>• See correct answers immediately</p>
                            <p>• Review explanations</p>
                            <p>• Navigate freely between questions</p>
                            <p>• Perfect for learning</p>
                        </div>
                        
                        <div class="mode-card" data-mode="exam">
                            <h3>Real Exam Mode</h3>
                            <p>• No immediate feedback</p>
                            <p>• Results shown at the end</p>
                            <p>• Simulates actual exam conditions</p>
                            <p>• Test your knowledge</p>
                        </div>
                    </div>


                    <div id="randomization-options" class="randomization-options hidden">
                        <h4>🎲 Randomization Options</h4>
                        <div class="randomization-controls">
                            <div class="randomization-option">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="randomize-questions" checked>
                                    <span class="checkmark"></span>
                                    Randomize Question Order
                                </label>
                                <p class="option-description">Questions will appear in random order</p>
                            </div>
                            <div class="randomization-option">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="randomize-answers" checked>
                                    <span class="checkmark"></span>
                                    Randomize Answer Choices
                                </label>
                                <p class="option-description">Answer options (A, B, C, D) will be shuffled</p>
                            </div>
                        </div>
                    </div>

                    <div class="start-actions">
                        <button id="start-exam" class="btn btn-primary" disabled>Start Exam</button>
                        <button id="view-answers" class="btn btn-secondary">📖 View All Answers</button>
                    </div>
                </div>
            </div>

            <!-- Question Screen -->
            <div id="question-screen" class="screen">
                <div class="question-container">
                    <div class="question-header">
                        <div class="question-title-row">
                            <div class="question-number">
                                Question <span id="current-question">1</span> of <span id="total-questions">60</span>
                            </div>
                            <div class="question-controls">
                                <div id="practice-controls" class="practice-controls hidden">
                                    <button id="show-answer" class="btn btn-info btn-sm">Show Answer</button>
                                    <button id="hide-answer" class="btn btn-secondary btn-sm hidden">Hide Answer</button>
                                    <button id="clear-answer" class="btn btn-warning btn-sm">Clear Answer</button>
                                </div>

                                <div id="exam-controls" class="exam-controls hidden">
                                    <button id="submit-practice" class="btn btn-warning btn-sm">🏁 Submit</button>
                                    <button id="submit-exam" class="btn btn-warning btn-sm">🏁 Submit</button>
                                    <button id="finish-exam" class="btn btn-warning btn-sm">Finish Exam</button>
                                </div>
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                    </div>
                    
                    <div class="question-content">
                        <h3 id="question-text">Loading question...</h3>
                        <div id="question-options" class="options-container">
                            <!-- Options will be dynamically generated -->
                        </div>
                    </div>
                    
                    <div id="feedback-section" class="feedback-section hidden">
                        <div class="feedback-content">
                            <div id="feedback-result" class="feedback-result"></div>
                            <div id="feedback-explanation" class="feedback-explanation"></div>
                        </div>
                    </div>

                    <div id="community-voting-section" class="community-voting-section hidden">
                        <div class="community-voting-content">
                            <h4>📊 Community Voting (ExamTopics)</h4>
                            <div id="community-voting-data" class="community-voting-data">
                                <!-- Community voting data will be displayed here -->
                            </div>
                            <p class="voting-note">
                                <small>💡 Correct answers are determined based on community voting from ExamTopics users</small>
                            </p>
                        </div>
                    </div>



                    <div class="question-navigation">
                        <button id="prev-question" class="btn btn-secondary">Previous</button>
                        <button id="next-question" class="btn btn-primary">Next</button>
                        <button id="submit-answer" class="btn btn-success">Submit Answer</button>
                    </div>
                </div>
            </div>

            <!-- Answers Screen -->
            <div id="answers-screen" class="screen">
                <div class="answers-container">
                    <h2>📖 All Questions & Answers</h2>
                    <p class="answers-description">Complete reference guide with all questions and correct answers</p>

                    <div class="answers-controls">
                        <input type="text" id="answer-search" placeholder="🔍 Search questions..." class="search-input">
                        <select id="answer-filter" class="filter-select">
                            <option value="all">All Questions</option>
                            <option value="single">Single Choice</option>
                            <option value="multiple">Multiple Choice</option>
                        </select>
                    </div>

                    <div id="answers-list" class="answers-list">
                        <!-- Answers will be dynamically generated -->
                    </div>

                    <div class="answers-actions">
                        <button id="back-to-start" class="btn btn-primary">🏠 Back to Start</button>
                    </div>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="results-screen" class="screen">
                <div class="results-container">
                    <h2>Exam Results</h2>
                    
                    <div class="score-summary">
                        <div class="score-circle">
                            <div class="score-percentage" id="score-percentage">0%</div>
                            <div class="score-fraction" id="score-fraction">0/60</div>
                        </div>

                        <div class="score-details">
                            <div class="score-item">
                                <span class="label">Correct:</span>
                                <span id="correct-count" class="value">0</span>
                            </div>
                            <div class="score-item">
                                <span class="label">Incorrect:</span>
                                <span id="incorrect-count" class="value">0</span>
                            </div>
                            <div class="score-item">
                                <span class="label">Unanswered:</span>
                                <span id="unanswered-count" class="value">0</span>
                            </div>
                        </div>
                    </div>

                    <div id="exam-summary" class="exam-summary">
                        <h3>📊 Exam Summary</h3>
                        <div class="summary-content">
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Total Questions:</span>
                                    <span id="total-questions-summary" class="stat-value">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Questions Answered:</span>
                                    <span id="answered-questions-summary" class="stat-value">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Questions Unanswered:</span>
                                    <span id="unanswered-questions-summary" class="stat-value">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Correct Answers:</span>
                                    <span id="correct-answers-summary" class="stat-value">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Final Score:</span>
                                    <span id="final-score-summary" class="stat-value">0%</span>
                                </div>
                            </div>
                            <div id="unanswered-warning" class="unanswered-warning hidden">
                                <div class="warning-content">
                                    <span class="warning-icon">⚠️</span>
                                    <div class="warning-text">
                                        <strong>Attention:</strong> You have <span id="unanswered-count-warning">0</span> unanswered questions.
                                        <br>These questions were marked as incorrect in your final score.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="results-actions">
                        <button id="review-answers" class="btn btn-primary">Review Answers</button>
                        <button id="restart-exam" class="btn btn-secondary">Start New Exam</button>
                    </div>
                    
                    <div id="review-section" class="review-section hidden">
                        <h3>Question Review</h3>
                        <div id="review-list" class="review-list">
                            <!-- Review items will be dynamically generated -->
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay hidden">
            <div class="loading-spinner"></div>
            <p>Loading exam data...</p>
        </div>

        <!-- Score Popup Modal -->
        <div id="score-popup" class="modal hidden">
            <div class="modal-content score-popup-content">
                <h3>🎯 Exam Complete!</h3>
                <div class="popup-score-summary">
                    <div class="popup-score-circle">
                        <div class="popup-score-percentage" id="popup-score-percentage">0%</div>
                        <div class="popup-score-fraction" id="popup-score-fraction">0/60</div>
                    </div>
                    <div class="popup-score-details">
                        <div class="popup-score-item correct">
                            <span class="popup-label">✅ Correct:</span>
                            <span id="popup-correct-count" class="popup-value">0</span>
                        </div>
                        <div class="popup-score-item incorrect">
                            <span class="popup-label">❌ Incorrect:</span>
                            <span id="popup-incorrect-count" class="popup-value">0</span>
                        </div>
                        <div class="popup-score-item unanswered">
                            <span class="popup-label">⚪ Unanswered:</span>
                            <span id="popup-unanswered-count" class="popup-value">0</span>
                        </div>
                    </div>
                </div>
                <div class="popup-actions">
                    <button id="view-detailed-results" class="btn btn-primary">📊 View Detailed Results</button>
                    <button id="close-score-popup" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>

        <!-- Error Modal -->
        <div id="error-modal" class="modal hidden">
            <div class="modal-content">
                <h3>Error</h3>
                <p id="error-message">An error occurred.</p>
                <button id="close-error" class="btn btn-primary">OK</button>
            </div>
        </div>
    </div>

    <script src="exam-questions.js?v=2.2"></script>
    <script src="script.js"></script>
</body>
</html>
