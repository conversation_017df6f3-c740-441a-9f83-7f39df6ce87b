# VCF Exam Practice Applications

This repository contains two VMware Cloud Foundation (VCF) exam practice applications:

## 📚 Available Exams

### 1. VCF Administrator Exam (2V0-11.25)
- **Location**: `administrator exam/` folder
- **Questions**: 60 practice questions
- **Features**: Complete exam simulator with practice and real exam modes

### 2. VCF Architect Exam (2V0-13.24)
- **Location**: `architect exam/` folder
- **Questions**: 60 complete practice questions (extracted from ExamTopics)
- **Features**: Community voting data integration, practice and real exam modes

## 🌟 Key Features

### VCF Architect Exam (NEW!)
- **Community Voting Integration**: Questions include community voting data from ExamTopics
- **Prioritized Answers**: Correct answers determined by community vote distribution
- **Visual Voting Display**: See how the community voted on each question
- **ExamTopics Source**: Questions extracted from actual ExamTopics 2V0-13.24 exam data

### Both Applications Include:
- **Two Exam Modes**:
  - Practice Mode: Immediate feedback and explanations
  - Real Exam Mode: Results shown at completion
- **Randomization Options**:
  - Randomize question order
  - Randomize answer choices
- **Progress Tracking**: Visual progress indicators and scoring
- **Theme Support**: Multiple visual themes (gradient, dark, light)
- **Answer Review**: Complete reference guide with search and filtering
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Getting Started

1. **For VCF Administrator Exam**:
   ```
   Open: administrator exam/index.html
   ```

2. **For VCF Architect Exam**:
   ```
   Open: architect exam/index.html
   ```

## 📊 Community Voting Feature (Architect Exam)

The VCF Architect exam includes unique community voting data that shows:
- How many users voted for each answer option
- Vote percentages and distribution
- Most voted answers highlighted
- Comparison between official answers and community consensus

This helps you understand not just the correct answers, but also how the community interprets each question.

## 🎯 Exam Information

### VCF Administrator (2V0-11.25)
- **Full Name**: VMware Cloud Foundation Administrator
- **Questions**: 60 comprehensive practice questions
- **Focus**: VCF administration, management, and operations

### VCF Architect (2V0-13.24)
- **Full Name**: VMware Cloud Foundation 5.2 Architect
- **Questions**: 60 complete questions with community voting data
- **Focus**: VCF architecture, design decisions, and planning
- **Source**: Complete ExamTopics question bank with community data
- **Coverage**: All exam topics including design principles, requirements gathering, and solution architecture

## 🔧 Technical Details

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **No Backend Required**: Runs entirely in the browser
- **Data Storage**: Local storage for preferences and progress
- **Responsive**: Mobile-friendly design

## 📝 Notes

- Questions in the architect exam are sourced from ExamTopics and include real community voting data
- Community voting helps identify questions where there may be multiple valid interpretations
- Both applications work offline once loaded
- Progress and preferences are saved locally in your browser

## 🤝 Contributing

Feel free to contribute additional questions, improvements, or bug fixes to either exam application.
